import win32com.client
import time
import os
import logging
import getpass
import win32clipboard
from datetime import datetime


def setup_logger(log_file=None):
    """Logging yapılandırması"""
    if log_file is None:
        # Varsayılan log dosya adı: extra_terminal_YYYYMMDD.log
        log_folder = "logs"
        if not os.path.exists(log_folder):
            os.makedirs(log_folder)
        log_file = os.path.join(
            log_folder, f"extra_terminal_{datetime.now().strftime('%Y%m%d')}.log"
        )

    # Logger yapılandırması
    logger = logging.getLogger("ExtraTerminal")
    logger.setLevel(logging.DEBUG)

    # Eğer handler'lar zaten ekle<PERSON>, tekrar ekleme
    if not logger.handlers:
        # Dosya handler
        file_handler = logging.FileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(logging.DEBUG)

        # Konsol handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)  # Konsola sadece INFO ve üstü

        # Format
        formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Handlerleri logger'a ekle
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

    return logger


# Global logger
logger = setup_logger()


class ExtraConnection:
    def __init__(self):
        """EXTRA! X-treme bağlantı sınıfı"""
        # Terminal sabitleri
        self.SCREEN_WIDTH = 80
        self.SCREEN_HEIGHT = 24
        self.DEFAULT_WAIT_TIME = 1.5
        self.SCREEN_STABILITY_COUNT = 2
        self.MAX_PAGES = 10

        # Terminal nesneleri
        self.extra = None
        self.session = None
        self.screen = None
        self.connected = False
        self.environment = None  # Seçilen ortam
        # Logger referansını tut
        self.logger = logger

    def connect(self):
        """Açık EXTRA! X-treme oturumuna bağlan"""
        try:
            # COM nesnesi oluştur
            self.extra = win32com.client.Dispatch("EXTRA.System")
            self.logger.info("EXTRA.System nesnesi oluşturuldu")

            # Aktif session'ı bul
            if self.extra.Sessions.Count > 0:
                self.logger.info(f"Açık session sayısı: {self.extra.Sessions.Count}")

                try:
                    self.session = self.extra.ActiveSession
                    self.logger.info("Aktif session bulundu")
                except:
                    self.logger.warning(
                        "Aktif session bulunamadı, ilk session deneniyor..."
                    )
                    try:
                        self.session = self.extra.Sessions(1)
                    except:
                        self.session = self.extra.Sessions.Item(0)

                # Screen nesnesini al
                self.screen = self.session.Screen
                self.logger.info("Screen nesnesine erişildi")

                self.connected = True
                self.logger.info("EXTRA! session bağlantısı tamam.")

                # Mevcut ekranı kontrol et - VTAM seçim paneli varsa
                self.check_vtam_selection_panel()

                return True
            else:
                self.logger.error("Hiç açık session bulunamadı.")
                return False
        except Exception as e:
            self.logger.error(f"EXTRA! bağlantı hatası: {str(e)}")
            return False

    def check_vtam_selection_panel(self):
        """VTAM Uygulama Seçme paneli kontrolü ve seçim"""
        if not self.connected:
            return False

        # Ekran içeriğini al
        screen_text = self.get_screen_text()

        # VTAM seçim panelini kontrol et
        if (
            "UTSAIP" in screen_text
            and "SECME" in screen_text
            and "PANELI" in screen_text
        ):
            self.logger.info("\n*** THY VTAM UYGULAMA SECME PANELI tespit edildi ***")
            self.logger.info("Ortam seçimi yapılacak.")

            # Kullanıcıya ortam seçimi için sor veya varsayılan olarak A kullan
            env_selection = input("Ortam seçin (A-ALCSPROD, B-CICSPROD, vb.) [A]: ")

            # Boş ise varsayılan olarak A kullan
            if not env_selection:
                env_selection = "A"
                self.logger.info(f"Varsayılan ortam seçildi: {env_selection}-ALCSPROD")
            else:
                self.logger.info(f"Seçilen ortam: {env_selection}")

            # Seçimi kaydet
            self.environment = env_selection

            # Seçimi gönder
            self.send_text(env_selection, 0.5)
            self.send_enter(3)

            # Seçim sonrası ekranın stabil olmasını bekle
            self.wait_for_stable_screen(5)
            return True
        else:
            self.logger.info("VTAM Uygulama Seçme paneli görünmüyor.")
            return False

    def get_screen_text_clipboard(self):
        """Panoya kopyalayarak ekran içeriğini al - daha güvenilir yöntem"""
        if not self.connected:
            return None

        try:
            # İçeriği kopyalamadan önce ekrana tıkla/odaklan
            try:
                self.screen.SetFocus()
            except:
                pass

            # Tüm ekranı seç
            try:
                self.screen.SelectAll()
            except:
                self.logger.warning("SelectAll hatası, Select metodu deneniyor...")
                try:
                    self.screen.Select(1, 1, 24, 80)  # Tüm ekran (1,1) -> (24,80)
                except Exception as e:
                    self.logger.error(f"Ekran seçme hatası: {str(e)}")
                    return None

            # Kopyala (Ctrl+C)
            try:
                self.screen.Copy()
                time.sleep(0.5)
            except:
                self.logger.warning("Copy metodu hatası, SendKeys kullanılıyor...")
                try:
                    import pyautogui

                    pyautogui.hotkey("ctrl", "c")
                    time.sleep(0.5)
                except:
                    self.logger.error("Kopyalama başarısız")
                    return None

            # Panodan içeriği al
            try:
                win32clipboard.OpenClipboard()
                if win32clipboard.IsClipboardFormatAvailable(win32clipboard.CF_TEXT):
                    content = win32clipboard.GetClipboardData(win32clipboard.CF_TEXT)
                    win32clipboard.CloseClipboard()

                    # Bytes ise decode et
                    if isinstance(content, bytes):
                        content = content.decode(
                            "cp1254", errors="replace"
                        )  # Türkçe karakter desteği

                    self.logger.debug(
                        f"Ekran içeriği başarıyla alındı ({len(content)} karakter)"
                    )
                    return content
                else:
                    win32clipboard.CloseClipboard()
                    self.logger.warning("Panoda metin verisi yok")
                    return None
            except Exception as e:
                try:
                    win32clipboard.CloseClipboard()
                except:
                    pass
                self.logger.error(f"Pano erişim hatası: {str(e)}")
                return None

        except Exception as e:
            self.logger.error(f"Clipboard ile ekran alma hatası: {str(e)}")
            return None

    def get_screen_text(self):
        """Clipboard yöntemini kullanan ekran okuma"""
        result = self.get_screen_text_clipboard() or "[Ekran içeriği alınamadı]"
        # Sonuç kontrolü
        if result and result != "[Ekran içeriği alınamadı]":
            # Ekran çıktısını temizle
            cleaned_result = self.clean_screen_output(result)
            return cleaned_result
        else:
            self.logger.error("Ekran içeriği alınamadı!")
            return "[Komut çalıştırıldı ancak sonuç alınamadı]"

    def wait_for_string(self, text, timeout=10):
        """Ekranda belirli bir metin görünene kadar bekle"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            screen_text = self.get_screen_text()
            if screen_text and text in screen_text:
                return True
            time.sleep(0.5)
        return False

    def wait_for_stable_screen(self, timeout=5):
        """
        Ekran içeriği değişmeyene kadar bekle
        Stabilite testi ile kopyalama tekrarını engeller

        Args:
            timeout (int): Maksimum bekleme süresi (saniye)

        Returns:
            bool: Ekran stabil olduysa True, olmadıysa False
        """
        self.logger.debug(f"Stable screen bekleniyor... (timeout: {timeout}s)")
        start_time = time.time()
        last_screen = self.get_screen_text()
        time.sleep(0.5)

        stability_count = 0  # Ekran içeriğinin kaç kez aynı kaldığını sayar
        required_stability = (
            self.SCREEN_STABILITY_COUNT
        )  # Kaç kez aynı içerik görülürse stabil kabul edilecek

        while time.time() - start_time < timeout:
            current_screen = self.get_screen_text()

            # Ekran içeriği aynıysa stabilite sayacını artır
            if current_screen == last_screen:
                stability_count += 1
                self.logger.debug(
                    f"Ekran sabit kaldı ({stability_count}/{required_stability})"
                )

                # Belirtilen sayıda aynı içerik görüldüyse stabil kabul et
                if stability_count >= required_stability:
                    self.logger.info("Ekran stabil duruma geldi")
                    return True
            else:
                # İçerik değiştiyse sayacı sıfırla
                self.logger.debug("Ekran içeriği değişti, sayaç sıfırlandı")
                stability_count = 0

            last_screen = current_screen
            time.sleep(0.5)

        self.logger.warning(f"Ekran {timeout} saniye içinde stabil olmadı!")
        return False

    def send_text(self, text, wait_after=0.5):
        """Host'a metin gönder"""
        if not self.connected:
            self.logger.error("Bağlantı yok!")
            return False

        try:
            # Önce odak alın
            try:
                self.screen.SetFocus()
                time.sleep(0.2)
            except:
                pass

            # Metni gönder
            self.screen.SendKeys(text)
            if wait_after > 0:
                time.sleep(wait_after)
            return True
        except Exception as e:
            self.logger.error(f"Metin gönderme hatası: {str(e)}")
            try:
                # Alternatif olarak pyautogui ile deneyin
                import pyautogui

                pyautogui.typewrite(text)
                if wait_after > 0:
                    time.sleep(wait_after)
                return True
            except:
                return False

    def send_enter(self, wait_after=1.5):
        """Enter tuşu gönder"""
        if not self.connected:
            self.logger.error("Bağlantı yok!")
            return False

        try:
            self.screen.SendKeys("<Enter>")
            if wait_after > 0:
                time.sleep(wait_after)
            return True
        except Exception as e:
            self.logger.error(f"Enter gönderme hatası: {str(e)}")
            try:
                # Alternatif olarak pyautogui ile deneyin
                import pyautogui

                pyautogui.press("enter")
                if wait_after > 0:
                    time.sleep(wait_after)
                return True
            except:
                return False

    def send_clear(self, wait_after=1.5):
        """Clear tuşu gönder"""
        if not self.connected:
            self.logger.error("Bağlantı yok!")
            return False

        try:
            self.screen.SendKeys("<Clear>")
            if wait_after > 0:
                time.sleep(wait_after)
            return True
        except Exception as e:
            self.logger.error(f"Clear gönderme hatası: {str(e)}")
            return False

    def login(self, username=None, password=None):
        """SON/ komutu ile giriş yap - Bağlantıdan sonraki işlem"""
        if not self.connected:
            self.logger.error("Bağlantı yok! Login yapılamıyor.")
            return False

        try:
            # Kullanıcı adı ve şifre sağlanmadıysa iste
            if username is None:
                username = input("TROYA kullanıcı adı: ")
            if password is None:
                password = getpass.getpass("TROYA şifre: ")

            # Ekranı temizle
            self.send_clear(2)

            # SON/username komutunu gönder (ENTRY öneki olmadan)
            login_command = f"SON/{username}"
            self.logger.info(f"Login komutu gönderiliyor: {login_command}")
            self.send_text(login_command)
            self.send_enter(2)

            # Şifre ekranını kontrol et
            screen_text = self.get_screen_text()
            self.logger.debug("Mevcut ekran: " + screen_text[:200])

            if "PASSWORD" in screen_text:
                self.logger.info("Şifre ekranı tespit edildi. Şifre giriliyor...")

                # Ekranı temizlemeden doğrudan şifreyi gir
                self.send_text(password, 0.5)
                self.logger.debug("Şifre girildi, Enter tuşu gönderiliyor...")
                self.send_enter(3)

                # Şifre giriş sonucunu kontrol et
                result = self.wait_for_stable_screen(5)
                self.logger.debug(f"Ekran durumu: {'Stabil' if result else 'Değişken'}")

                new_screen = self.get_screen_text()
                self.logger.debug("Giriş sonrası ekran: " + new_screen[:200])

                if "INVALID" in new_screen or "ERROR" in new_screen:
                    self.logger.error("Giriş başarısız!")
                    return False
                else:
                    self.logger.info("Giriş başarılı.")
                    return True
            else:
                self.logger.error("Şifre ekranı gelmedi!")
                self.logger.debug(f"Mevcut ekran: {screen_text}")
                return False

        except Exception as e:
            self.logger.exception(f"Giriş hatası: {str(e)}")
            return False

    def clean_screen_output(self, screen_text, command_type=None):
        """
        Terminal ekranından gereksiz boş satırları ve sistem bilgilerini temizler

        Arguments:
            screen_text: Temizlenecek ekran çıktısı
            command_type: Komut türü (farklı komutlar için farklı temizleme kuralları uygulanabilir)
        """
        if not screen_text:
            return ""

        # Satırlara böl
        lines = screen_text.split("\r\n")

        # Genel temizleme (boş satırları ve sistem satırlarını kaldır)
        meaningful_lines = []
        for line in lines:
            line = line.rstrip()  # Satır sonundaki boşlukları kaldır

            # Boş satırları atla
            if not line.strip():
                continue

            # Sistem bilgisi satırlarını atla
            if "TROYA" in line:
                continue

            meaningful_lines.append(line)

        # Temizlenmiş çıktıyı birleştir
        cleaned_output = "\n".join(meaningful_lines)
        return cleaned_output

    def _has_more_content(self, screen_text):
        """Ekranda daha fazla içerik olup olmadığını kontrol et

        Args:
            screen_text (str): Mevcut ekran içeriği

        Returns:
            bool: Daha fazla içerik varsa True, yoksa False
        """
        has_exclamation = "!" in screen_text
        has_plus = "+" in screen_text
        has_more = "MORE" in screen_text

        self.logger.debug(
            f"Devam sayfası göstergeleri: ! = {has_exclamation}, + = {has_plus}, MORE = {has_more}"
        )

        return has_exclamation or has_plus or has_more

    def _navigate_next_page(self, current_screen):
        """Sonraki sayfaya geçmeyi dene

        Args:
            current_screen (str): Mevcut ekran içeriği

        Returns:
            tuple: (başarı_durumu, yeni_ekran_içeriği)
        """
        # Önce MD komutunu dene
        self.logger.info("MD ile kaydırılıyor...")
        self.send_text("MD", 0.5)
        self.send_enter(2)

        if self.wait_for_stable_screen(3):
            next_screen = self.get_screen_text()
            if next_screen and next_screen != current_screen:
                return True, next_screen

        # MD başarısız olduysa PF8'i dene
        self.logger.info("MD çalışmadı, PF8 tuşu deneniyor...")
        try:
            self.screen.SendKeys("<PF8>")
            time.sleep(self.DEFAULT_WAIT_TIME)

            if self.wait_for_stable_screen(3):
                next_screen = self.get_screen_text()
                if next_screen != current_screen:
                    return True, next_screen

            self.logger.warning("PF8 komutu da işlemedi, navigasyon yapılamıyor.")
            return False, current_screen
        except Exception as e:
            self.logger.error(f"PF8 gönderirken hata: {str(e)}")
            return False, current_screen

    def execute_entry(self, command, wait_stable=True):
        """Komutu çalıştır - ENTRY öneki olmadan

        Args:
            command (str): Çalıştırılacak komut
            wait_stable (bool): True ise ekranın stabil olmasını bekleyip tüm sayfaları kontrol eder.
                            False ise maksimum hızda tek ekran çeker.

        Returns:
            str: Komut çıktısı veya hata mesajı
        """
        if not self.connected:
            self.logger.error("Bağlantı yok!")
            return None

        try:
            # Ekranı temizle
            self.send_clear(2)

            # Komutu doğrudan gönder (ENTRY öneki olmadan)
            self.logger.info(f"Komut çalıştırılıyor: {command}")
            self.send_text(command)
            self.send_enter(1)  # Hızlı mod için bekleme süresini azalt

            # HIZLI MOD - wait_stable False ise maksimum hızda çalış
            if not wait_stable:
                self.logger.debug("Hızlı mod: Stabil ekran bekleme atlandı")
                time.sleep(0.5)  # Minimum bekleme süresi
                result = self.get_screen_text()
                if result and result != "[Ekran içeriği alınamadı]":
                    return result
                else:
                    return "[Komut çalıştırıldı ancak sonuç alınamadı]"

            # NORMAL MOD - wait_stable True ise stabil ekran bekle ve tüm sayfaları topla
            else:
                self.logger.debug("Normal mod: Ekranın stabil olması bekleniyor...")
                self.wait_for_stable_screen(5)

                # Ekran sonucunu al
                result = self.get_screen_text()
                all_screens = []

                if result and result != "[Ekran içeriği alınamadı]":
                    all_screens.append(result)

                    # Devamı varsa sayfaları topla
                    more_content = self._has_more_content(result)
                    page_count = 1

                    while more_content and page_count < self.MAX_PAGES:
                        self.logger.info(
                            f"Ek sayfa algılandı ({page_count+1}), kaydırılıyor..."
                        )

                        # Sayfayı kaydır ve yeni içerik al
                        navigation_success, next_screen = self._navigate_next_page(
                            result
                        )

                        if not navigation_success:
                            self.logger.warning(
                                "Sayfa kaydırma başarısız, işlem sonlandırılıyor."
                            )
                            break

                        if next_screen not in all_screens:
                            all_screens.append(next_screen)
                            page_count += 1
                            result = next_screen  # Sonraki döngü için güncelle
                            more_content = self._has_more_content(next_screen)
                        else:
                            self.logger.warning("Bu içerik zaten listeye eklenmiş!")
                            more_content = False

                    # Tüm ekranları birleştir
                    complete_result = "\n--- NEXT PAGE ---\n".join(all_screens)
                    return complete_result
                else:
                    self.logger.error("Ekran içeriği alınamadı!")
                    return "[Komut çalıştırıldı ancak sonuç alınamadı]"

        except Exception as e:
            self.logger.exception(f"Komut çalıştırma hatası: {str(e)}")
            return f"HATA: {str(e)}"
