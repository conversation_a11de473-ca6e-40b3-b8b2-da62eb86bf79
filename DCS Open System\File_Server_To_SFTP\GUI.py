import tkinter as tk
from tkinter import filedialog, messagebox
import os
import zipfile
import paramiko
from getpass import getpass
from datetime import datetime, timedelta
import shutil


def select_network_folder():
    folder = filedialog.askdirectory()
    if folder:
        entry_network.delete(0, tk.END)
        entry_network.insert(0, folder)


def select_temp_folder():
    folder = filedialog.askdirectory()
    if folder:
        entry_temp.delete(0, tk.END)
        entry_temp.insert(0, folder)


def run_process():
    network_folder = entry_network.get().strip()
    local_temp_folder = entry_temp.get().strip()
    ucus_nolari = [u.strip() for u in entry_ucus.get() if u.strip()]
    ucus_tarihi = entry_tarih.get().strip()
    sftp_host = entry_host.get().strip()
    sftp_port = int(entry_port.get().strip())
    sftp_user = entry_user.get().strip()
    sftp_pass = entry_pass.get().strip()
    sftp_target_folder = entry_target.get().strip()

    if not all(
        [
            network_folder,
            local_temp_folder,
            ucus_nolari,
            ucus_tarihi,
            sftp_host,
            sftp_port,
            sftp_user,
            sftp_pass,
            sftp_target_folder,
        ]
    ):
        messagebox.showerror("Hata", "Tüm alanları doldurun!")
        return

    os.makedirs(local_temp_folder, exist_ok=True)
    txt_files_to_upload = []

    # Uçuş tarihi için 0 ve +1 günleri hesapla
    ucus_tarihi_dt = datetime.strptime(ucus_tarihi, "%Y%m%d")
    tarih_listesi = [
        (ucus_tarihi_dt + timedelta(days=offset)).strftime("%Y%m%d")
        for offset in [0, 1]
    ]

    # ZIP dosyalarını tara ve TXT dosyalarını çıkart (tarih filtreli)
    for fname in os.listdir(network_folder):
        if fname.startswith("DCSHISTORY-GUNLUK-") and fname.endswith(".zip"):
            tarih = fname.split("-")[2].split(".")[0]
            if tarih_listesi[0] <= tarih <= tarih_listesi[1]:
                zip_path = os.path.join(network_folder, fname)
                with zipfile.ZipFile(zip_path, "r") as z:
                    for txtname in z.namelist():
                        if txtname.endswith(".TXT"):
                            local_txt_path = os.path.join(local_temp_folder, txtname)
                            if local_txt_path in txt_files_to_upload:
                                continue  # Aynı dosya zaten eklendi
                            with z.open(txtname) as f_in:
                                content = f_in.read()
                                found = False
                                for line in content.decode("utf-8").splitlines():
                                    parts = line.split("|")
                                    if (
                                        len(parts) > 1
                                        and parts[0] in ucus_nolari
                                        and parts[1] == ucus_tarihi
                                    ):
                                        found = True
                                        break
                                if found:
                                    with open(local_txt_path, "wb") as f_out:
                                        f_out.write(content)
                                    txt_files_to_upload.append(local_txt_path)

    if txt_files_to_upload:
        transport = paramiko.Transport((sftp_host, sftp_port))
        transport.connect(username=sftp_user, password=sftp_pass)
        sftp = paramiko.SFTPClient.from_transport(transport)
        for txt_path in txt_files_to_upload:
            remote_path = f"{sftp_target_folder}/{os.path.basename(txt_path)}"
            print(f"Yükleniyor: {txt_path} -> {remote_path}")
            sftp.put(txt_path, remote_path)
        sftp.close()
        transport.close()
        # .temp klasöründeki dosyaları sil
        for file in os.listdir(local_temp_folder):
            file_path = os.path.join(local_temp_folder, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
        os.rmdir(local_temp_folder)
        print("Yükleme tamamlandı.")
    else:
        print("Belirtilen uçuş ve tarihe ait dosya bulunamadı.")


root = tk.Tk()
root.title("DCS TXT SFTP Yükleyici")

tk.Label(root, text="Ağ Klasörü:").grid(row=0, column=0, sticky="e")
entry_network = tk.Entry(root, width=40)
entry_network.grid(row=0, column=1)
tk.Button(root, text="Seç", command=select_network_folder).grid(row=0, column=2)

tk.Label(root, text="Temp Klasörü:").grid(row=1, column=0, sticky="e")
entry_temp = tk.Entry(root, width=40)
entry_temp.insert(0, "./temp")
entry_temp.grid(row=1, column=1)
tk.Button(root, text="Seç", command=select_temp_folder).grid(row=1, column=2)

tk.Label(root, text="Uçuş Nolari:").grid(row=2, column=0, sticky="e")
entry_ucus = tk.Entry(root)
entry_ucus.grid(row=2, column=1)

tk.Label(root, text="Uçuş Tarihi (YYYYMMDD):").grid(row=3, column=0, sticky="e")
entry_tarih = tk.Entry(root)
entry_tarih.grid(row=3, column=1)

tk.Label(root, text="SFTP Host:").grid(row=4, column=0, sticky="e")
entry_host = tk.Entry(root)
entry_host.insert(0, "thysftp.thy.com")
entry_host.grid(row=4, column=1)

tk.Label(root, text="SFTP Port:").grid(row=5, column=0, sticky="e")
entry_port = tk.Entry(root)
entry_port.insert(0, "22")
entry_port.grid(row=5, column=1)

tk.Label(root, text="SFTP Kullanıcı:").grid(row=6, column=0, sticky="e")
entry_user = tk.Entry(root)
entry_user.grid(row=6, column=1)

tk.Label(root, text="SFTP Şifre:").grid(row=7, column=0, sticky="e")
entry_pass = tk.Entry(root, show="*")
entry_pass.grid(row=7, column=1)

tk.Label(root, text="SFTP Hedef Klasör:").grid(row=8, column=0, sticky="e")
entry_target = tk.Entry(root)
entry_target.insert(0, "IN")
entry_target.grid(row=8, column=1)

tk.Button(root, text="Başlat", command=run_process, bg="green", fg="white").grid(
    row=9, column=1, pady=10
)

root.mainloop()
