{"cells": [{"cell_type": "code", "execution_count": 7, "id": "f2d6bee0", "metadata": {}, "outputs": [], "source": ["#!pip install selenium pandas"]}, {"cell_type": "code", "execution_count": 8, "id": "ba772e6a", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.common.exceptions import TimeoutException\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": 9, "id": "302ff266", "metadata": {}, "outputs": [], "source": ["def check_ldap_status(login_list):\n", "    # Sonuçları saklamak için boş liste\n", "    results = []\n", "\n", "    # Configure Chrome options\n", "    chrome_options = webdriver.ChromeOptions()\n", "    chrome_options.add_argument(\"--ignore-certificate-errors\")\n", "    chrome_options.add_argument(\"--ignore-ssl-errors\")\n", "\n", "    # Initialize Chrome driver with service\n", "    service = webdriver.ChromeService()\n", "    driver = webdriver.Chrome(service=service, options=chrome_options)\n", "\n", "    try:\n", "        # Login sayfasına git\n", "        driver.get(\"https://www2.thy.com/tanim/login.tk\")\n", "        print(\"Login sayfası açıldı.\")\n", "        \n", "        # E<PERSON>er auth.thy.com'a yönlendirildiyse manual login için bekle ve \n", "        # tekrar https://www2.thy.com/tanim/login.tk sayfasına dönene kadar izle\n", "        current_url = driver.current_url\n", "        if \"auth.thy.com/auth-web/login.jsp\" in current_url:\n", "            print(\"Yönlendirme tespit edildi. Manuel giri<PERSON> bekleniyor...\")\n", "            \n", "            # 30 saniye içinde kullanıcının giriş yapmasını bekle\n", "            wait_time = 30\n", "            print(f\"{wait_time} saniye içinde giriş yapın.\")\n", "            \n", "            target_url = \"https://www2.thy.com/tanim/login.tk\"\n", "            start_time = time.time()\n", "            \n", "            # Orijinal URL'e geri dönene kadar bekle\n", "            while \"tanim/login.tk\" not in driver.current_url:\n", "                if time.time() - start_time > 120:  # 2 dakika maksim<PERSON> bekleme s<PERSON>i\n", "                    print(\"Zaman aşımı: Orijinal sayfaya dönüş yapılamadı.\")\n", "                    break\n", "                time.sleep(1)\n", "            \n", "            print(\"Login işlemi tamamlandı, devam ediliyor...\")\n", "        else:\n", "            # <PERSON><PERSON><PERSON> yönlendirme yoksa normal 30 saniye bekle\n", "            print(\"Lütfen 30 saniye içinde giriş yapın.\")\n", "            time.sleep(30)\n", "\n", "        # <PERSON><PERSON> list<PERSON><PERSON><PERSON> her login i<PERSON><PERSON> kontrol\n", "        for login in login_list:\n", "            try:\n", "                # LDAP kontrol sayfasına git\n", "                driver.get(\n", "                    \"https://www2.thy.com/tanim/ldapControlAction.tk?page=default\"\n", "                )\n", "\n", "                # Login input alanını bul ve değeri gir\n", "                login_input = WebDriverWait(driver, 10).until(\n", "                    EC.presence_of_element_located((By.NAME, \"loginName\"))\n", "                )\n", "                login_input.clear()\n", "                login_input.send_keys(login)\n", "\n", "                # Search butonunu bul ve tıkla\n", "                search_button = driver.find_element(\n", "                    By.XPATH, \"//input[@value='SEARCH']\"\n", "                )\n", "                search_button.click()\n", "\n", "                # Sonuçlar için kısa bir bekleme\n", "                time.sleep(2)\n", "\n", "                # Tüm form alanlarını topla\n", "                form_data = {\n", "                    \"search_login\": login,  # <PERSON><PERSON> login\n", "                    \"timestamp\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),  # <PERSON><PERSON><PERSON> zamanı\n", "                }\n", "\n", "                # LDAP bilgileri\n", "                ldap_fields = [\n", "                    \"loginName\",\n", "                    \"givenName\",\n", "                    \"sn\",\n", "                    \"nonThyEmail\",\n", "                    \"companyCode\",\n", "                    \"title\",\n", "                    \"stat2\",\n", "                    \"distinguished<PERSON>ame\",\n", "                ]\n", "\n", "                # HRTANIM bilgileri\n", "                hrtanim_fields = [\n", "                    \"hrTanimMerni\",\n", "                    \"hrTanimVorna\",\n", "                    \"hrTanimNachn\",\n", "                    \"hrTanimStat2\",\n", "                    \"hrTanimMailPers\",\n", "                    \"hrTanimBukrs\",\n", "                    \"hrTanimJobText\",\n", "                    \"hrTanimLtext\",\n", "                    \"hrTanimZmhlad\",\n", "                    \"hrTanimCityCode\",\n", "                    \"hrTanimAedat\",\n", "                    \"hrTanimBegDate\",\n", "                    \"hrTanimEndDate\",\n", "                    \"hrTanimUserLocale\",\n", "                    \"hrTanimUsrId\",\n", "                ]\n", "\n", "                # Tüm alan<PERSON>ı topla\n", "                for field_name in ldap_fields + hrtanim_fields:\n", "                    try:\n", "                        element = driver.find_element(By.NAME, field_name)\n", "                        form_data[field_name] = element.get_attribute(\"value\")\n", "                    except:\n", "                        form_data[field_name] = \"\"\n", "\n", "                results.append(form_data)\n", "                print(f\"Processed login: {login}\")\n", "\n", "            except TimeoutException:\n", "                print(f\"Timeout error for login: {login}\")\n", "                results.append(\n", "                    {\n", "                        \"search_login\": login,\n", "                        \"error\": \"Timeout error\",\n", "                        \"timestamp\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "                    }\n", "                )\n", "            except Exception as e:\n", "                print(f\"Error processing login {login}: {str(e)}\")\n", "                results.append(\n", "                    {\n", "                        \"search_login\": login,\n", "                        \"error\": str(e),\n", "                        \"timestamp\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "                    }\n", "                )\n", "\n", "    finally:\n", "        # <PERSON><PERSON><PERSON><PERSON><PERSON> kapat\n", "        driver.quit()\n", "\n", "        # Sonuçları DataFrame'e çevir\n", "        df = pd.DataFrame(results)\n", "\n", "        # Excel'e kaydet\n", "        output_file = f'ldap_results_{time.strftime(\"%Y%m%d_%H%M%S\")}.xlsx'\n", "        df.to_excel(output_file, index=False)\n", "        print(f\"Results saved to {output_file}\")\n", "\n", "        return df"]}, {"cell_type": "code", "execution_count": 10, "id": "1a8b8cc8", "metadata": {}, "outputs": [], "source": ["# Yeni işleme fonksiyonu - NULL LoginName'leri işler ve yeni login oluşturur\n", "def process_login_list(excel_file):\n", "    # Excel dosyasını oku\n", "    df = pd.read_excel(excel_file, sheet_name=\"Sheet1\")\n", "    \n", "    # Hata kayıtları için boş liste oluştur\n", "    error_records = []\n", "    \n", "    # <PERSON><PERSON>z kayıtlar için yeni DataFrame\n", "    clean_df = df.copy()\n", "    rows_to_drop = []\n", "    \n", "    # OLMASI_GEREKEN_LOGIN sütunu yoksa o<PERSON>\n", "    if 'OLMASI_GEREKEN_LOGIN' not in clean_df.columns:\n", "        clean_df['OLMASI_GEREKEN_LOGIN'] = None\n", "    \n", "    # Her kayıt i<PERSON>in i<PERSON> yap\n", "    for index, row in df.iterrows():\n", "        # LOGIN_NAME null (NaN) ise\n", "        if pd.isna(row['LOGIN_NAME']):\n", "            try:\n", "                pcc_city_code = str(row['PCC_CITY_CODE']).strip()\n", "                isim = str(row['ISIM']).strip()\n", "                soyisim = str(row['SOYISIM']).strip()\n", "                \n", "                # Soyisimde boşluk kontrolü\n", "                if ' ' in soyisim:\n", "                    # Hata kaydını ekle\n", "                    error_record = row.to_dict()\n", "                    error_record['ERROR_REASON'] = \"SOYISIM içinde boşluk var\"\n", "                    error_records.append(error_record)\n", "                    \n", "                    # <PERSON><PERSON><PERSON> ka<PERSON><PERSON><PERSON><PERSON>\n", "                    rows_to_drop.append(index)\n", "                    continue\n", "                \n", "                # Yeni login oluştur: PCC_CITY_CODE + ISIM'in ilk harfi + SOYISIM\n", "                if len(isim) > 0 and len(soyisim) > 0:\n", "                    new_login = f\"{pcc_city_code}{isim[0].upper()}{soyisim.upper()}\"\n", "                    clean_df.at[index, 'OLMASI_GEREKEN_LOGIN'] = new_login\n", "                else:\n", "                    # İsim veya soyisim eksikse hata kaydı ekle\n", "                    error_record = row.to_dict()\n", "                    error_record['ERROR_REASON'] = \"İsim veya soyisim eksik\"\n", "                    error_records.append(error_record)\n", "                    rows_to_drop.append(index)\n", "            \n", "            except Exception as e:\n", "                # <PERSON><PERSON><PERSON> hat<PERSON>\n", "                error_record = row.to_dict()\n", "                error_record['ERROR_REASON'] = f\"İşlem hatası: {str(e)}\"\n", "                error_records.append(error_record)\n", "                rows_to_drop.append(index)\n", "        else:\n", "            # LOGI<PERSON>_NAME varsa, OLMASI_GEREKEN_LOGIN'e kopyala\n", "            clean_df.at[index, 'OLMASI_GEREKEN_LOGIN'] = row['LOGIN_NAME']\n", "    \n", "    # Hatalı kayıtları temiz DF'den çıkar\n", "    clean_df = clean_df.drop(rows_to_drop)\n", "    \n", "    # Hata kayıtlarını DataFrame'e dönüştür\n", "    error_df = pd.DataFrame(error_records) if error_records else pd.DataFrame()\n", "    \n", "    # İşlenmiş Excel'i kaydet\n", "    output_file = f'processed_logins_{time.strftime(\"%Y%m%d_%H%M%S\")}.xlsx'\n", "    with pd.ExcelWriter(output_file) as writer:\n", "        clean_df.to_excel(writer, sheet_name='<PERSON><PERSON><PERSON>_<PERSON>', index=False)\n", "        if not error_df.empty:\n", "            error_df.to_excel(writer, sheet_name='<PERSON><PERSON>_<PERSON>', index=False)\n", "    \n", "    print(f\"İşlenmiş dosya kaydedildi: {output_file}\")\n", "    print(f\"Toplam kayıt: {len(df)}, <PERSON><PERSON><PERSON> kayıt: {len(clean_df)}, <PERSON><PERSON><PERSON> kayıt: {len(error_df)}\")\n", "    \n", "    # <PERSON><PERSON><PERSON> ka<PERSON><PERSON>tl<PERSON>an login listesini dö<PERSON>ü<PERSON> (LDAP kontrolü için)\n", "    return clean_df['OLMASI_GEREKEN_LOGIN'].tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "28002845", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["İşlenmiş dosya kaydedildi: processed_logins_20250429_144324.xlsx\n", "Toplam kayıt: 2, <PERSON><PERSON><PERSON> kayıt: 1, <PERSON><PERSON><PERSON> kayıt: 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Error sending stats to Plausible: error sending request for url (https://plausible.io/api/event)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Login sayfası açıldı.\n", "Yönlendirme tespit edildi. Manuel giri<PERSON> bekleniyor...\n", "30 saniye içinde giriş yapın.\n", "<PERSON>gin işlemi ta<PERSON>, devam ediliyor...\n", "Processed login: nanTTEST\n", "Results saved to ldap_results_20250429_144437.xlsx\n"]}], "source": ["# <PERSON><PERSON><PERSON><PERSON> örneği\n", "excel_file = \"KULLANICI NULL.xlsx\"  # Excel dosya adını buraya girin\n", "# ESKİ YÖNTEM: login_list = read_login_list(excel_file)\n", "login_list = process_login_list(excel_file)  # YENİ YÖNTEM\n", "results_df = check_ldap_status(login_list)"]}, {"cell_type": "code", "execution_count": 13, "id": "a807f404", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "search_login", "rawType": "object", "type": "string"}, {"name": "timestamp", "rawType": "object", "type": "string"}, {"name": "loginName", "rawType": "object", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "rawType": "object", "type": "string"}, {"name": "sn", "rawType": "object", "type": "string"}, {"name": "nonThyEmail", "rawType": "object", "type": "string"}, {"name": "companyCode", "rawType": "object", "type": "string"}, {"name": "title", "rawType": "object", "type": "string"}, {"name": "stat2", "rawType": "object", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "rawType": "object", "type": "string"}, {"name": "hrTanimMerni", "rawType": "object", "type": "string"}, {"name": "hrTanimVorna", "rawType": "object", "type": "string"}, {"name": "hrTanimNachn", "rawType": "object", "type": "string"}, {"name": "hrTanimStat2", "rawType": "object", "type": "string"}, {"name": "hrTanimMailPers", "rawType": "object", "type": "string"}, {"name": "hrTanimBukrs", "rawType": "object", "type": "string"}, {"name": "hrTanimJobText", "rawType": "object", "type": "string"}, {"name": "hrTanimLtext", "rawType": "object", "type": "string"}, {"name": "hrTanimZmhlad", "rawType": "object", "type": "string"}, {"name": "hrTanimCityCode", "rawType": "object", "type": "string"}, {"name": "hrTanimAedat", "rawType": "object", "type": "string"}, {"name": "hrTanimBegDate", "rawType": "object", "type": "string"}, {"name": "hrTanimEndDate", "rawType": "object", "type": "string"}, {"name": "hrTanimUserLocale", "rawType": "object", "type": "string"}, {"name": "hrTanimUsrId", "rawType": "object", "type": "string"}], "conversionMethod": "pd.DataFrame", "ref": "a446fe70-3ea5-4c96-89d6-7cbcd63a960c", "rows": [["0", "nanTTEST", "2025-04-29 14:44:34", "NANTTEST", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "shape": {"columns": 25, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>search_login</th>\n", "      <th>timestamp</th>\n", "      <th>loginName</th>\n", "      <th>givenName</th>\n", "      <th>sn</th>\n", "      <th>nonThyEmail</th>\n", "      <th>companyCode</th>\n", "      <th>title</th>\n", "      <th>stat2</th>\n", "      <th>distinguishedName</th>\n", "      <th>...</th>\n", "      <th>hrTanimBukrs</th>\n", "      <th>hrTanimJobText</th>\n", "      <th>hrTanimLtext</th>\n", "      <th>hrTanimZmhlad</th>\n", "      <th>hrTanimCityCode</th>\n", "      <th>hrTanimAedat</th>\n", "      <th>hrTanimBegDate</th>\n", "      <th>hrTanimEndDate</th>\n", "      <th>hrTanimUserLocale</th>\n", "      <th>hrTanimUsrId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>nanTTEST</td>\n", "      <td>2025-04-29 14:44:34</td>\n", "      <td>NANTTEST</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 25 columns</p>\n", "</div>"], "text/plain": ["  search_login            timestamp loginName givenName sn nonThyEmail  \\\n", "0     nanTTEST  2025-04-29 14:44:34  NANTTEST                            \n", "\n", "  companyCode title stat2 distinguishedName  ... hrTanimBukrs hrTanimJobText  \\\n", "0                                            ...                               \n", "\n", "  hrTanimLtext hrTanimZmhlad hrTanimCityCode hrTanimAedat hrTanimBegDate  \\\n", "0                                                                          \n", "\n", "  hrTanimEndDate hrTanimUserLocale hrTanimUsrId  \n", "0                                                \n", "\n", "[1 rows x 25 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["results_df"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}