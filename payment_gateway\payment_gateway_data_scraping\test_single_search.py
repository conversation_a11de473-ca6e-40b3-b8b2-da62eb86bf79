#!/usr/bin/env python3
"""
THY Payment Gateway tek arama testi

Bu script tek bir PNR veya Order ID ile arama yapmak için kullanılır.
"""

from PaymentGatewayScrap import PaymentGatewayScrap
import json


def test_single_search():
    """Tek arama testi yapar"""
    scraper = None
    try:
        print("=== THY Payment Gateway Tek Arama Testi ===\n")

        # Scraper'ı başlat (auto_login=False çünkü manuel login yapacağız)
        print("1. Chrome tarayıcısı başlatılıyor...")
        scraper = PaymentGatewayScrap(auto_login=False)

        print("2. Login sayfasına yönlendiriliyor...")
        scraper.login()

        # Login durumunu kontrol et
        if not scraper.is_logged_in:
            print("\n⚠️  Manuel login gerekli!")
            print("Tarayıcıda açılan sayfada login yapın ve Enter'a basın...")
            input("Login tamamlandıktan sonra Enter'a basın: ")
            scraper.is_logged_in = True

        print("\n3. Arama testi başlatılıyor...")

        # Test parametreleri
        test_cases = [
            {
                "search_value": "UGYGLE",
                "search_type": "pnr",
                "transaction_type": "AA",
                "data_type": "response",
                "get_all_results": False,
                "description": "Tek sonuç - Response",
            },
            {
                "search_value": "UGYGLE",
                "search_type": "pnr",
                "transaction_type": "AA",
                "data_type": "both",
                "get_all_results": False,
                "description": "Tek sonuç - Both",
            },
            {
                "search_value": "UGYGLE",
                "search_type": "pnr",
                "transaction_type": "AA",
                "data_type": "both",
                "get_all_results": True,
                "description": "Tüm sonuçlar - Both",
            },
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- Test {i}: {test_case['description']} ---")

            result = scraper.search_transaction(
                search_value=test_case["search_value"],
                search_type=test_case["search_type"],
                transaction_type=test_case["transaction_type"],
                data_type=test_case["data_type"],
                get_all_results=test_case["get_all_results"],
            )

            # Sonuç analizi
            if isinstance(result, dict) and "total_results" in result:
                print(f"Toplam sonuç sayısı: {result['total_results']}")
                print(f"Alınan sonuç sayısı: {len(result['results'])}")
            else:
                print(f"Tek sonuç alındı: {type(result)}")

            print(f"Sonuç özeti: {str(result)[:200]}...")

            # Sonraki test için sayfa yenile
            if i < len(test_cases):
                print("Sayfa yenileniyor...")
                scraper.driver.refresh()
                import time

                time.sleep(2)

        print("\n✅ Tüm testler tamamlandı!")

    except Exception as e:
        print(f"\n❌ Hata oluştu: {e}")
        import traceback

        traceback.print_exc()

    finally:
        if scraper:
            print("\nTarayıcı kapatılıyor...")
            scraper.close()


if __name__ == "__main__":
    test_single_search()
