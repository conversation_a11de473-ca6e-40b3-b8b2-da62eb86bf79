#!/usr/bin/env python3
"""
THY Payment Gateway Bulk İşlem Scripti

Bu script order.xlsx dosyasındaki tüm order'ları işler ve sonuçları Excel'e kaydeder.
"""

from PaymentGatewayScrap import PaymentGatewayScrap
import pandas as pd
import os
from datetime import datetime


def create_sample_excel(file_type="order"):
    """Örnek Excel dosyası oluşturur"""
    if file_type == "order":
        sample_data = {"Order": ["12345", "67890", "ABC123", "DEF456", "GHI789"]}
        filename = "order.xlsx"
    else:  # pnr
        sample_data = {"PNR": ["UGYGLE", "ABCDEF", "XYZPQR", "MNOPQR", "STUVWX"]}
        filename = "pnr.xlsx"

    df = pd.DataFrame(sample_data)
    df.to_excel(filename, index=False)
    print(f"Örnek {filename} dosyası oluşturuldu.")


def bulk_process():
    """Bulk işlem yapar - Order veya PNR"""
    scraper = None
    try:
        print("=== THY Payment Gateway Bulk İşlem ===\n")

        # Dosya tipini belirle
        print("Hangi tip dosya ile çalışacaksınız?")
        print("1. Order dosyası (order.xlsx)")
        print("2. PNR dosyası (pnr.xlsx)")

        file_choice = input("Seçiminiz (1-2): ")

        if file_choice == "2":
            filename = "pnr.xlsx"
            search_type = "pnr"
            file_type = "pnr"
        else:
            filename = "order.xlsx"
            search_type = "order"
            file_type = "order"

        # Excel dosyasını kontrol et
        if not os.path.exists(filename):
            print(f"{filename} dosyası bulunamadı.")
            create_sample = input("Örnek dosya oluşturulsun mu? (y/n): ")
            if create_sample.lower() == "y":
                create_sample_excel(file_type)
                print(f"Örnek {filename} dosyayı düzenleyip tekrar çalıştırın.")
                return
            else:
                print("İşlem iptal edildi.")
                return

        # Dosyayı kontrol et
        df = pd.read_excel(filename)
        expected_column = "Order" if search_type == "order" else "PNR"

        if expected_column not in df.columns:
            print(f"Hata: '{expected_column}' sütunu bulunamadı.")
            print(f"Mevcut sütunlar: {list(df.columns)}")
            print(f"Dosyada '{expected_column}' sütunu olmalı.")
            return

        print(f"Excel dosyası yüklendi: {len(df)} {search_type} bulundu.")
        print("İlk 5 satır:")
        print(df.head())

        # Kullanıcıdan onay al
        print(f"\nToplam {len(df)} {search_type} işlenecek.")
        confirm = input("Devam etmek istiyor musunuz? (y/n): ")
        if confirm.lower() != "y":
            print("İşlem iptal edildi.")
            return

        # İşlem tipini sor
        print("\nİşlem seçenekleri:")
        print("1. Sadece Response (Hızlı)")
        print("2. Hem Request hem Response (Detaylı)")
        print("3. Tüm sonuçları al (Çoklu sonuç varsa)")

        choice = input("Seçiminiz (1-3): ")

        if choice == "1":
            data_type = "response"
            get_all_results = False
        elif choice == "2":
            data_type = "both"
            get_all_results = False
        elif choice == "3":
            data_type = "both"
            get_all_results = True
        else:
            print("Geçersiz seçim, varsayılan olarak Response seçildi.")
            data_type = "response"
            get_all_results = False

        # Transaction type sor
        transaction_type = input(
            "Transaction type (AA, HH, vs. - boş bırakabilirsiniz): "
        ).strip()
        if not transaction_type:
            transaction_type = None

        # Scraper'ı başlat
        print("\n1. Chrome tarayıcısı başlatılıyor...")
        scraper = PaymentGatewayScrap(data_file=filename, auto_login=True)

        # Login kontrolü
        if not scraper.is_logged_in:
            print("Manuel login gerekli.")
            input("Login tamamlandıktan sonra Enter'a basın: ")
            scraper.is_logged_in = True

        print("\n2. Bulk işlem başlatılıyor...")
        print(f"   - Search type: {search_type}")
        print(f"   - Data type: {data_type}")
        print(f"   - Get all results: {get_all_results}")
        print(f"   - Transaction type: {transaction_type or 'Tümü'}")

        # İşlemi başlat
        scraper.process_all_orders(
            transaction_type=transaction_type,
            data_type=data_type,
            get_all_results=get_all_results,
            search_type=search_type,
        )

        # Sonuçları kaydet
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"search_results_{timestamp}.xlsx"

        print(f"\n3. Sonuçlar kaydediliyor: {output_file}")
        scraper.save_results(output_file)

        # Özet bilgi
        print(f"\n✅ İşlem tamamlandı!")
        print(f"   - İşlenen {search_type} sayısı: {len(df)}")
        print(f"   - Çıktı dosyası: {output_file}")
        print(f"   - Final satır sayısı: {len(scraper.df)}")

        # İlk birkaç sonucu göster
        print("\nİlk 3 sonuç:")
        display_columns = [expected_column]
        if "Result_Index" in scraper.df.columns:
            display_columns.append("Result_Index")
        if "Total_Results" in scraper.df.columns:
            display_columns.append("Total_Results")
        if "Status" in scraper.df.columns:
            display_columns.append("Status")

        print(scraper.df[display_columns].head(3))

    except Exception as e:
        print(f"\n❌ Hata oluştu: {e}")
        import traceback

        traceback.print_exc()

    finally:
        if scraper:
            print("\nTarayıcı kapatılıyor...")
            scraper.close()


def quick_test():
    """Hızlı test - sadece ilk 2 order'ı işler"""
    scraper = None
    try:
        print("=== Hızlı Test (İlk 2 Order) ===\n")

        if not os.path.exists("order.xlsx"):
            create_sample_excel()

        # Scraper'ı başlat
        scraper = PaymentGatewayScrap(data_file="order.xlsx", auto_login=False)

        # Sadece ilk 2 satırı al
        scraper.df = scraper.df.head(2)
        print(f"Test için {len(scraper.df)} order seçildi.")

        # Login
        scraper.login()
        if not scraper.is_logged_in:
            input("Login tamamlandıktan sonra Enter'a basın: ")
            scraper.is_logged_in = True

        # İşlem
        scraper.process_all_orders(
            transaction_type="AA", data_type="response", get_all_results=False
        )

        # Sonuç
        scraper.save_results("test_results.xlsx")
        print("✅ Test tamamlandı: test_results.xlsx")

    except Exception as e:
        print(f"❌ Test hatası: {e}")

    finally:
        if scraper:
            scraper.close()


if __name__ == "__main__":
    print("THY Payment Gateway Bulk İşlem")
    print("1. Tam işlem (Order/PNR dosyası)")
    print("2. Hızlı test (İlk 2 kayıt)")

    choice = input("Seçiminiz (1-2): ")

    if choice == "2":
        quick_test()
    else:
        bulk_process()
