#!/usr/bin/env python3
"""
THY Payment Gateway Bulk İşlem Scripti

Bu script order.xlsx dosyasındaki tüm order'ları işler ve sonuçları Excel'e kaydeder.
"""

from PaymentGatewayScrap import PaymentGatewayScrap
import pandas as pd
import os
from datetime import datetime

def create_sample_excel():
    """Örnek order.xlsx dosyası oluşturur"""
    sample_data = {
        'Order': ['12345', '67890', 'ABC123', 'DEF456', 'GHI789']
    }
    df = pd.DataFrame(sample_data)
    df.to_excel('order.xlsx', index=False)
    print("Örnek order.xlsx dosyası oluşturuldu.")

def bulk_process_orders():
    """Bulk işlem yapar"""
    scraper = None
    try:
        print("=== THY Payment Gateway Bulk İşlem ===\n")
        
        # Excel dosyasını kontrol et
        if not os.path.exists('order.xlsx'):
            print("order.xlsx dosyası bulunamadı.")
            create_sample = input("Örnek dosya oluşturulsun mu? (y/n): ")
            if create_sample.lower() == 'y':
                create_sample_excel()
                print("Örnek dosyayı düzenleyip tekrar çalıştırın.")
                return
            else:
                print("İşlem iptal edildi.")
                return
        
        # Dosyayı kontrol et
        df = pd.read_excel('order.xlsx')
        print(f"Excel dosyası yüklendi: {len(df)} order bulundu.")
        print("İlk 5 satır:")
        print(df.head())
        
        # Kullanıcıdan onay al
        print(f"\nToplam {len(df)} order işlenecek.")
        confirm = input("Devam etmek istiyor musunuz? (y/n): ")
        if confirm.lower() != 'y':
            print("İşlem iptal edildi.")
            return
        
        # İşlem tipini sor
        print("\nİşlem seçenekleri:")
        print("1. Sadece Response (Hızlı)")
        print("2. Hem Request hem Response (Detaylı)")
        print("3. Tüm sonuçları al (Çoklu sonuç varsa)")
        
        choice = input("Seçiminiz (1-3): ")
        
        if choice == "1":
            data_type = "response"
            get_all_results = False
        elif choice == "2":
            data_type = "both"
            get_all_results = False
        elif choice == "3":
            data_type = "both"
            get_all_results = True
        else:
            print("Geçersiz seçim, varsayılan olarak Response seçildi.")
            data_type = "response"
            get_all_results = False
        
        # Transaction type sor
        transaction_type = input("Transaction type (AA, HH, vs. - boş bırakabilirsiniz): ").strip()
        if not transaction_type:
            transaction_type = None
        
        # Scraper'ı başlat
        print("\n1. Chrome tarayıcısı başlatılıyor...")
        scraper = PaymentGatewayScrap(data_file="order.xlsx", auto_login=True)
        
        # Login kontrolü
        if not scraper.is_logged_in:
            print("Manuel login gerekli.")
            input("Login tamamlandıktan sonra Enter'a basın: ")
            scraper.is_logged_in = True
        
        print("\n2. Bulk işlem başlatılıyor...")
        print(f"   - Data type: {data_type}")
        print(f"   - Get all results: {get_all_results}")
        print(f"   - Transaction type: {transaction_type or 'Tümü'}")
        
        # İşlemi başlat
        scraper.process_all_orders(
            transaction_type=transaction_type,
            data_type=data_type,
            get_all_results=get_all_results
        )
        
        # Sonuçları kaydet
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"search_results_{timestamp}.xlsx"
        
        print(f"\n3. Sonuçlar kaydediliyor: {output_file}")
        scraper.save_results(output_file)
        
        # Özet bilgi
        print(f"\n✅ İşlem tamamlandı!")
        print(f"   - İşlenen order sayısı: {len(df)}")
        print(f"   - Çıktı dosyası: {output_file}")
        print(f"   - Final satır sayısı: {len(scraper.df)}")
        
        # İlk birkaç sonucu göster
        print("\nİlk 3 sonuç:")
        display_columns = ['Order']
        if 'Result_Index' in scraper.df.columns:
            display_columns.append('Result_Index')
        if 'Total_Results' in scraper.df.columns:
            display_columns.append('Total_Results')
        if 'Status' in scraper.df.columns:
            display_columns.append('Status')
        
        print(scraper.df[display_columns].head(3))
        
    except Exception as e:
        print(f"\n❌ Hata oluştu: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if scraper:
            print("\nTarayıcı kapatılıyor...")
            scraper.close()

def quick_test():
    """Hızlı test - sadece ilk 2 order'ı işler"""
    scraper = None
    try:
        print("=== Hızlı Test (İlk 2 Order) ===\n")
        
        if not os.path.exists('order.xlsx'):
            create_sample_excel()
        
        # Scraper'ı başlat
        scraper = PaymentGatewayScrap(data_file="order.xlsx", auto_login=False)
        
        # Sadece ilk 2 satırı al
        scraper.df = scraper.df.head(2)
        print(f"Test için {len(scraper.df)} order seçildi.")
        
        # Login
        scraper.login()
        if not scraper.is_logged_in:
            input("Login tamamlandıktan sonra Enter'a basın: ")
            scraper.is_logged_in = True
        
        # İşlem
        scraper.process_all_orders(
            transaction_type="AA",
            data_type="response",
            get_all_results=False
        )
        
        # Sonuç
        scraper.save_results("test_results.xlsx")
        print("✅ Test tamamlandı: test_results.xlsx")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    print("THY Payment Gateway Bulk İşlem")
    print("1. Tam işlem (Tüm order'lar)")
    print("2. Hızlı test (İlk 2 order)")
    
    choice = input("Seçiminiz (1-2): ")
    
    if choice == "2":
        quick_test()
    else:
        bulk_process_orders()
