{"cells": [{"cell_type": "code", "execution_count": null, "id": "675cf6ac", "metadata": {}, "outputs": [], "source": ["#!pip install paramiko"]}, {"cell_type": "code", "execution_count": 24, "id": "8a1b09a8", "metadata": {}, "outputs": [], "source": ["import os\n", "import zipfile\n", "import paramiko\n", "from getpass import getpass\n", "from datetime import datetime, timedelta"]}, {"cell_type": "code", "execution_count": 48, "id": "6940102a", "metadata": {}, "outputs": [], "source": ["network_folder = input(\"<PERSON>ğ klasörü yolunu girin: \").strip()\n", "local_temp_folder = \"./temp\"\n", "ucus_nolari = (\n", "    input(\"Uçuş numaralarını girin (virg<PERSON><PERSON> a<PERSON>ı<PERSON>ı<PERSON>, örn: TK0140,TK0141): \")\n", "    .strip()\n", "    .split(\",\")\n", ")\n", "ucus_nolari = [u.strip() for u in ucus_nolari if u.strip()]\n", "ucus_tarihi = input(\"Uçuş tarihi girin (YYYYMMDD): \").strip()\n", "sftp_host = \"thysftp.thy.com\"\n", "sftp_port = 22\n", "sftp_user = input(\"SFTP kullanıcı adı: \").strip()\n", "sftp_pass = getpass(\"SFTP şifresi: \")\n", "sftp_target_folder = \"IN\"\n", "os.makedirs(local_temp_folder, exist_ok=True)\n", "txt_files_to_upload = []"]}, {"cell_type": "code", "execution_count": 54, "id": "a01c0a64", "metadata": {}, "outputs": [], "source": ["txt_files_to_upload = []"]}, {"cell_type": "code", "execution_count": 49, "id": "8d5ea5cc", "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON><PERSON> ta<PERSON><PERSON> -1, 0, +1 günleri hesapla\n", "ucus_tarihi_dt = datetime.strptime(ucus_tarihi, \"%Y%m%d\")\n", "tarih_listesi = [\n", "    (ucus_tarihi_dt + timedelta(days=offset)).strftime(\"%Y%m%d\")\n", "    for offset in [ 0, 1]\n", "]"]}, {"cell_type": "code", "execution_count": 55, "id": "9699cc04", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bulunan Dosya : ['./temp\\\\DCSHISTORY-GUNLUK-20250510.0400.TXT', './temp\\\\DCSHISTORY-GUNLUK-20250510.1400.TXT', './temp\\\\DCSHISTORY-GUNLUK-20250510.1700.TXT', './temp\\\\DCSHISTORY-GUNLUK-20250510.2000.TXT']\n"]}], "source": ["\n", "# ZIP dosyalarını tara ve TXT dosyalarını çıkart (tarih filtreli)\n", "for fname in os.listdir(network_folder):\n", "    if fname.startswith(\"DCSHISTORY-GUNLUK-\") and fname.endswith(\".zip\"):\n", "        tarih = fname.split(\"-\")[2].split(\".\")[0]\n", "        if tarih_listesi[0] <= tarih <= tarih_listesi[1]:\n", "            zip_path = os.path.join(network_folder, fname)\n", "            with zipfile.ZipFile(zip_path, \"r\") as z:\n", "                for txtname in z.namelist():\n", "                    if txtname.endswith(\".TXT\"):\n", "                        local_txt_path = os.path.join(local_temp_folder, txtname)\n", "                        if local_txt_path in txt_files_to_upload:\n", "                            continue  # <PERSON><PERSON><PERSON> dosya zaten eklendi\n", "                        with z.open(txtname) as f_in:\n", "                            content = f_in.read()\n", "                            found = False\n", "                            for line in content.decode(\"utf-8\").splitlines():\n", "                                parts = line.split(\"|\")\n", "                                if (\n", "                                    len(parts) > 1\n", "                                    and parts[0] in ucus_nolari\n", "                                    and parts[1] == ucus_tarihi\n", "                                ):\n", "                                    found = True\n", "                                    break\n", "                            if found:\n", "                                with open(local_txt_path, \"wb\") as f_out:\n", "                                    f_out.write(content)\n", "                                txt_files_to_upload.append(local_txt_path)\n", "#\n", "print(f\"<PERSON><PERSON><PERSON> : {str(txt_files_to_upload)}\")"]}, {"cell_type": "code", "execution_count": 56, "id": "11c24501", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Yükleniyor: ./temp\\DCSHISTORY-GUNLUK-20250510.0400.TXT -> IN/DCSHISTORY-GUNLUK-20250510.0400.TXT\n", "Yükleniyor: ./temp\\DCSHISTORY-GUNLUK-20250510.1400.TXT -> IN/DCSHISTORY-GUNLUK-20250510.1400.TXT\n", "Yükleniyor: ./temp\\DCSHISTORY-GUNLUK-20250510.1700.TXT -> IN/DCSHISTORY-GUNLUK-20250510.1700.TXT\n", "Yükleniyor: ./temp\\DCSHISTORY-GUNLUK-20250510.2000.TXT -> IN/DCSHISTORY-GUNLUK-20250510.2000.TXT\n", "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>landı.\n"]}], "source": ["# SFTP ile dosyaları yükle\n", "if txt_files_to_upload:\n", "    transport = paramiko.Transport((sftp_host, sftp_port))\n", "    transport.connect(username=sftp_user, password=sftp_pass)\n", "    sftp = paramiko.SFTPClient.from_transport(transport)\n", "    for txt_path in txt_files_to_upload:\n", "        remote_path = f\"{sftp_target_folder}/{os.path.basename(txt_path)}\"\n", "        print(f\"Yükleniyor: {txt_path} -> {remote_path}\")\n", "        sftp.put(txt_path, remote_path)\n", "    sftp.close()\n", "    transport.close()\n", "    # .temp klasöründeki dosyaları sil\n", "    for file in os.listdir(local_temp_folder):\n", "        file_path = os.path.join(local_temp_folder, file)\n", "        if os.path.isfile(file_path):\n", "            os.remove(file_path)\n", "    os.rmdir(local_temp_folder)\n", "    print(\"Yükleme tamamlandı.\")\n", "else:\n", "    print(\"Belirtilen uçuş ve tarihe ait dosya bulunamadı.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}