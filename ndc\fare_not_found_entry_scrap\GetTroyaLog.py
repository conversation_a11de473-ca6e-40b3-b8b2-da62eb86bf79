import time
import pandas as pd
import json
import requests
from requests.auth import HTTPBasicAuth
import xml.etree.ElementTree as ET
import re
import os
import getpass
from concurrent.futures import ThreadPoolExecutor, as_completed

# Basic authentication credentials
username = input("<PERSON><PERSON><PERSON><PERSON><PERSON> adı girin: ")
password = getpass.getpass("Şifrenizi girin: ")

# JSON dosyasını okuyun
with open("Error Data.json", "r", encoding="utf-8") as file:
    data = json.load(file)

# 'hits' içindeki veriyi çıkarın
hits_data = data["hits"]["hits"]

# 'hits' içindeki veriyi Pandas DataFrame'e dönüştürün
df = pd.json_normalize(hits_data)

###################################################### GRAYLOG Search ######################################################

# Graylog URL
url = "https://graylogprod.thy.com/api/views/search"

# İstek parametreleri
payload_template = {
    "queries": [
        {
            "query": {
                "type": "elasticsearch",
                "query_string": "",
            },
            "timerange": {"type": "relative", "range": 0},
            "search_types": [
                {
                    "limit": 150,
                    "offset": 0,
                    "sort": [{"field": "timestamp", "order": "DESC"}],
                    "fields": [],
                    "decorators": [],
                    "type": "messages",
                }
            ],
        }
    ]
}

jsondata = {"parameter_bindings": {}}

# Header bilgileri
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "X-Requested-By": "Graylog API",
}

namespace = {
    "ns2": "http://www.iata.org/IATA/2015/EASD/00/IATA_OffersAndOrdersCommonTypes"
}


def fetch_logs(index, trx_id):
    payload = payload_template.copy()
    payload["queries"][0]["query"][
        "query_string"
    ] = f'callerMethodName:getFares AND marker:TROYA_ENTRY AND "{trx_id}"'
    try:
        response = requests.post(
            url, json=payload, headers=headers, auth=(username, password), timeout=30
        )
        if response.status_code == 201:
            response_data = response.json()
            search_id = response_data["id"]

            execute_url = f"{url}/{search_id}/execute"
            execute_response = requests.post(
                execute_url,
                json=jsondata,
                headers=headers,
                auth=(username, password),
                timeout=30,
            )

            if execute_response.status_code == 201:
                execute_data = execute_response.json()
                result_id = list(execute_data["results"].keys())[0]
                search_types = execute_data["results"][result_id]["search_types"]
                random_id = list(search_types.keys())[0]
                messages = search_types[random_id]["messages"]
                if messages:
                    message1 = messages[0]["message"]["message"]
                    message2 = (
                        messages[1]["message"]["message"] if len(messages) > 1 else None
                    )
                    return (index, message1, message2)
        return (index, None, None)
    except Exception as e:
        print(f"{index} - {trx_id} için hata: {e}")
        return (index, None, None)


# Tüm trxId değerlerini gezmek için paralel döngü
futures = []
with ThreadPoolExecutor(max_workers=10) as executor:
    for index, row in df.iterrows():
        trx_id = row["_source.trxId"]
        futures.append(executor.submit(fetch_logs, index, trx_id))

    for future in as_completed(futures):
        index, message1, message2 = future.result()
        df.at[index, "log1"] = message1
        df.at[index, "log2"] = message2
        print(f"{index} işlemi için loglar alındı.")

print("TROYA LOG ları Ayrıştırılıyor...")
# Yeni sütunlar ekleyin
df["log_warning_no_answer"] = None
df["log_warning_source"] = None
df["flight_segments"] = None  # Add flight segments column


def extract_flight_segments(log_text):
    """Extract flight segment lines from log text"""
    if pd.isna(log_text):
        return None

    # Find segments using regex with more relaxed pattern to catch all formats
    segments = []
    in_segment_section = False

    for line in log_text.split("\n"):
        # Look for NDC BOOKING header which typically appears before flight segments
        if "*** THIS IS AN NDC BOOKING ***" in line:
            in_segment_section = True
            continue

        # If we're in the segment section and line starts with space+number
        if in_segment_section and re.match(r"^\s*\d+\s+[A-Z0-9]+", line):
            segments.append(line.strip())

        # Exit segment section when we hit non-segment content after segments
        elif (
            in_segment_section
            and segments
            and not line.strip().startswith(" ")
            and line.strip()
            and not re.match(r"^\s*\d+", line)
        ):
            in_segment_section = False

    # Remove duplicates while preserving order
    unique_segments = []
    for segment in segments:
        if segment not in unique_segments:
            unique_segments.append(segment)

    return "\n".join(unique_segments) if unique_segments else None


# log1 ve log2 sütunlarındaki veriyi işleyin
for index, row in df.iterrows():
    # Process warning data
    for log_col in ["log1", "log2"]:
        log_data = row[log_col]
        if pd.notna(log_data):
            log_lines = log_data.split("\n")
            for i, line in enumerate(log_lines):
                if "WARNING-NO-ANSWER" in line:
                    if i >= 3:
                        df.at[index, "log_warning_no_answer"] = log_lines[i - 3]
                        df.at[index, "log_warning_source"] = log_col
                        df.at[index, "log_id"] = log_lines[i + 2]
                    break

    # Extract flight segments from both logs
    all_segments = []
    for log_col in ["log1", "log2"]:
        log_data = row[log_col]
        if pd.notna(log_data):
            segments = extract_flight_segments(log_data)
            if segments:
                all_segments.append(segments)

    # Combine segments and remove duplicates
    if all_segments:
        combined = "\n".join(all_segments)
        unique_lines = []
        for line in combined.split("\n"):
            if line and line not in unique_lines:
                unique_lines.append(line)

        df.at[index, "flight_segments"] = "\n".join(unique_lines)

# Create Data directory if it doesn't exist
os.makedirs("Data", exist_ok=True)

# Mevcut tarih ve saati alın
current_time = time.strftime("%d-%m-%Y")
# Veriyi Excel dosyasına aktarın
output_file = f"./Data/TROYA_LOG_{current_time}.xlsx"
df.to_excel(output_file, index=False)

print(f"Veri '{output_file}' dosyasına başarıyla aktarıldı.")
