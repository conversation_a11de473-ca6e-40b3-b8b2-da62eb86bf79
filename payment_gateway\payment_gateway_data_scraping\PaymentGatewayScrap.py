import pandas as pd
import time
import json
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class PaymentGatewayScrap:
    # XPATH sabitleri
    XPATHS = {
        "order_input": "//*[@id='j_idt23']/table[1]/tbody/tr[1]/td[2]/input",
        "pnr_input": "//*[@id='j_idt23']/table[1]/tbody/tr[1]/td[4]/input",
        "date_input": "j_idt30InputDate",
        "transaction_select": "//*[@id='j_idt23']/table[1]/tbody/tr[3]/td[2]/select",
        "search_button": "//input[@type='submit' and @value='Search']",
        "request_link": "//tr[starts-with(@id, 'modelList:0')]//a[text()='Request']",
        "response_link": "//tr[starts-with(@id, 'modelList:0')]//a[text()='Response']",
        "popup_container": "popup_container",
        "popup_data": "popupData",
        "close_button": "//p/a[text()='KAPAT']",
        "close_button_alt": "//div[@id='popup_header_controls']/a",
        "clean_button": "//div[text()='Clean']",
    }

    def __init__(
        self,
        url="https://www2.thy.com/pgadmin/admin/SearchTransaction.jsf#",
        data_file=None,
        transaction_type=None,
        auto_login=True,
    ):
        """
        THY PGW işlem sorgulama otomasyonu için gerekli kurulumları yapar.

        Args:
            url: THY admin sayfası URL'i
            data_file: Excel dosyası (opsiyonel)
            transaction_type: İşlem tipi filtresi (örn: "AA" için)
            auto_login: Otomatik login yapılsın mı
        """

        self.url = url
        self.data_file = data_file
        self.transaction_type = transaction_type
        self.auto_login = auto_login
        self.is_logged_in = False
        self.results = []
        self.df = None

        self.setup_driver()
        if data_file:
            self.load_data()
        if auto_login:
            self.login()

    def setup_driver(self):
        """Selenium için Chrome web sürücüsünü başlatır ve yapılandırır"""
        chrome_options = Options()
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--no-sandbox")

        service = Service()
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, 10)

    def load_data(self):
        """Excel dosyasını pandas DataFrame'e yükler"""
        if self.data_file and os.path.exists(self.data_file):
            self.df = pd.read_excel(self.data_file)
            print(f"Veri yüklendi: {len(self.df)} kayıt.")
        elif self.data_file:
            print(f"Excel dosyası bulunamadı: {self.data_file}")
        else:
            print("Veri dosyası belirtilmedi.")

    def ensure_logged_in(self):
        """Login durumunu kontrol eder ve gerekirse login yapar"""
        if not self.is_logged_in:
            self.login()

    def login(self, username="", password="", max_wait_time=60):
        """
        Siteye giriş yapar ve kimlik doğrulama sayfası kontrolü yapar

        Args:
            username: Giriş kullanıcı adı
            password: Giriş şifresi
            max_wait_time: Kimlik doğrulama sayfasından çıkış için maksimum bekleme süresi (saniye)
        """
        try:
            self.driver.get(self.url)
            print("Sayfaya giriş yapılıyor...")

            # Sayfanın yüklenmesini bekle
            time.sleep(2)

            # Kimlik doğrulama sayfasına yönlendirme kontrolü
            auth_url = "https://auth.thy.com/auth-web/login.jsp"
            current_url = self.driver.current_url

            if auth_url in current_url:
                print(f"Kimlik doğrulama sayfasına yönlendirildi: {current_url}")

                # Login bilgileri verildiyse giriş yap
                if username and password:
                    # Kullanıcı adı ve şifre alanlarını bul
                    try:
                        username_field = self.wait.until(
                            EC.presence_of_element_located((By.ID, "username"))
                        )
                        password_field = self.wait.until(
                            EC.presence_of_element_located((By.ID, "password"))
                        )
                        login_button = self.wait.until(
                            EC.element_to_be_clickable(
                                (By.XPATH, "//*[@id='btn_login']")
                            )
                        )

                        username_field.send_keys(username)
                        password_field.send_keys(password)
                        login_button.click()
                    except Exception as e:
                        print(f"Giriş alanları bulunamadı veya giriş yapılamadı: {e}")

                # Kimlik doğrulama sayfasından çıkış yapılana kadar bekle
                print(
                    f"Kimlik doğrulama işlemi bekleniyor (maksimum {max_wait_time} saniye içinde işlem yapılması gerekmektedir)..."
                )

                start_time = time.time()
                while auth_url in self.driver.current_url:
                    time.sleep(2)
                    if time.time() - start_time > max_wait_time:
                        print(
                            f"Kimlik doğrulama zaman aşımına uğradı ({max_wait_time} saniye)."
                        )
                        break

                print(f"Mevcut URL: {self.driver.current_url}")

            # Ana sayfa yüklendikten sonra ek bekleme
            time.sleep(3)
            self.is_logged_in = True
            print("Login başarılı.")

        except Exception as e:
            print(f"Giriş yaparken hata oluştu: {e}")
            self.is_logged_in = False

    def search_transaction(
        self,
        search_value,
        search_type="order",
        transaction_type=None,
        data_type="response",
    ):
        """
        Belirtilen sipariş numarası veya PNR ile arama yapar ve sonuçları getirir

        Args:
            search_value: Aranacak sipariş numarası veya PNR
            search_type: Arama tipi ("order" veya "pnr")
            transaction_type: Bu arama için özel işlem tipi filtresi
            data_type: Alınacak veri tipi ("response", "request", "both")

        Returns:
            dict: Arama sonucunda dönen response/request verisi veya her ikisi
        """
        try:
            # Login kontrolü
            self.ensure_logged_in()

            # Input alanını belirle ve doldur
            if search_type == "order":
                print(f"ORDER ID numarası aranıyor: {search_value}")
                input_xpath = self.XPATHS["order_input"]
            elif search_type == "pnr":
                print(f"PNR aranıyor: {search_value}")
                input_xpath = self.XPATHS["pnr_input"]
            else:
                raise ValueError(
                    f"Geçersiz arama tipi: {search_type}. 'order' veya 'pnr' olmalı."
                )

            input_element = self.wait.until(
                EC.presence_of_element_located((By.XPATH, input_xpath))
            )
            input_element.clear()
            input_element.send_keys(search_value)

            # Date alanını temizle
            self._clear_date_field()

            # Transaction type filtresi uygula
            self._apply_transaction_filter(transaction_type)

            # Arama butonunu bul ve tıkla
            search_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.XPATHS["search_button"]))
            )
            search_button.click()

            # Sonuçların yüklenmesini bekle
            time.sleep(2)

            # Data type'a göre veri al
            if data_type.lower() == "both":
                return self._get_both_data()
            else:
                return self._get_single_data(data_type)

        except Exception as e:
            print(f"Arama sırasında hata oluştu: {e}")
            return {"error": str(e)}

    def _clear_date_field(self):
        """Date alanını temizler"""
        try:
            date_input = self.driver.find_element(By.ID, self.XPATHS["date_input"])
            if date_input.get_attribute("value"):
                print("Date alanı değeri bulundu, temizleniyor...")
                date_input.click()

                clean_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, self.XPATHS["clean_button"]))
                )
                clean_button.click()
                print("Tarih alanı temizlendi.")
                time.sleep(1)
        except (NoSuchElementException, TimeoutException):
            print("Date alanı bulunamadı veya temizlenemedi, devam ediliyor.")
        except Exception as e:
            print(f"Tarih temizlenirken hata oluştu: {e}, devam ediliyor.")

    def _apply_transaction_filter(self, transaction_type):
        """Transaction type filtresi uygular"""
        filter_value = (
            transaction_type if transaction_type is not None else self.transaction_type
        )

        if filter_value:
            try:
                transaction_select = self.driver.find_element(
                    By.XPATH, self.XPATHS["transaction_select"]
                )
                select = Select(transaction_select)
                select.select_by_value(filter_value)
                print(f"İşlem tipi filtresi uygulandı: {filter_value}")
            except Exception as e:
                print(
                    f"İşlem tipi filtresi uygulanırken hata oluştu: {e}, filtresiz devam ediliyor."
                )

    def _get_single_data(self, data_type):
        """Tek bir veri tipini (request veya response) alır"""
        try:
            # Data type'a göre uygun linki bul
            if data_type.lower() == "response":
                link_xpath = self.XPATHS["response_link"]
                link_text = "Response"
            elif data_type.lower() == "request":
                link_xpath = self.XPATHS["request_link"]
                link_text = "Request"
            else:
                raise ValueError(
                    f"Geçersiz data tipi: {data_type}. 'response' veya 'request' olmalı."
                )

            data_link = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, link_xpath))
            )
            data_link.click()

            # Popup'ın açılmasını bekle ve veriyi al
            return self._extract_popup_data(link_text)

        except TimeoutException:
            print(f"{data_type} linki bulunamadı veya popup açılmadı.")
            return {"error": f"No {data_type} found"}

    def _extract_popup_data(self, data_type_name):
        """Popup'tan veri çıkarır"""
        try:
            # Popup'ın açılmasını bekle
            self.wait.until(
                EC.presence_of_element_located((By.ID, self.XPATHS["popup_container"]))
            )

            # Popup içindeki textarea'dan veriyi al
            popup_data = self.wait.until(
                EC.presence_of_element_located((By.ID, self.XPATHS["popup_data"]))
            )
            data_text = popup_data.get_attribute("value")
            print(f"{data_type_name} alındı.")

            # Popup'ı kapat
            self._close_popup()

            # JSON formatına çevir
            try:
                return json.loads(data_text)
            except json.JSONDecodeError:
                print("JSON çözümlenemedi, ham metin dönüyor.")
                return {"raw_text": data_text}

        except Exception as e:
            print(f"Popup verisi alınırken hata: {e}")
            return {"error": str(e)}

    def _get_both_data(self):
        """Hem request hem response verilerini alır"""
        result = {"request": None, "response": None}

        # Mevcut linkleri kontrol et
        available_links = self._check_available_links()
        print(
            f"Mevcut linkler: {', '.join(available_links) if available_links else 'Hiçbiri'}"
        )

        # Request verisini al (eğer mevcutsa)
        if "Request" in available_links:
            try:
                result["request"] = self._get_single_data("request")
            except Exception as e:
                print(f"Request alırken hata: {e}")
                result["request"] = {"error": str(e)}
        else:
            result["request"] = {"error": "Request link not available"}

        # Response verisini al (eğer mevcutsa)
        if "Response" in available_links:
            try:
                result["response"] = self._get_single_data("response")
            except Exception as e:
                print(f"Response alırken hata: {e}")
                result["response"] = {"error": str(e)}
        else:
            result["response"] = {"error": "Response link not available"}

        return result

    def _check_available_links(self):
        """Mevcut linkleri kontrol eder"""
        available_links = []
        try:
            # Request linkini kontrol et
            request_links = self.driver.find_elements(
                By.XPATH, self.XPATHS["request_link"]
            )
            if request_links:
                available_links.append("Request")

            # Response linkini kontrol et
            response_links = self.driver.find_elements(
                By.XPATH, self.XPATHS["response_link"]
            )
            if response_links:
                available_links.append("Response")

        except Exception as e:
            print(f"Link kontrolü sırasında hata: {e}")

        return available_links

    def _close_popup(self):
        """Popup'ı kapatır"""
        try:
            close_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, self.XPATHS["close_button"]))
            )
            close_button.click()
            print("Popup kapatıldı.")
            time.sleep(1)
        except (NoSuchElementException, TimeoutException):
            print("Popup kapatma butonu bulunamadı, alternatif yöntem deneniyor...")
            try:
                close_button = self.driver.find_element(
                    By.XPATH, self.XPATHS["close_button_alt"]
                )
                close_button.click()
                print("Popup alternatif yöntemle kapatıldı.")
                time.sleep(1)
            except:
                print("Popup kapatılamadı, devam ediliyor.")

    def search_order(self, order_number, transaction_type=None):
        """
        Geriye uyumluluk için eski metod. search_transaction metodunu çağırır.

        Args:
            order_number: Aranacak sipariş numarası
            transaction_type: Bu arama için özel işlem tipi filtresi

        Returns:
            dict: Arama sonucunda dönen response verisi
        """
        return self.search_transaction(
            order_number, "order", transaction_type, "response"
        )

    def search_pnr(self, pnr_number, transaction_type=None, data_type="response"):
        """
        PNR ile arama yapmak için kolaylık metodu.

        Args:
            pnr_number: Aranacak PNR numarası
            transaction_type: Bu arama için özel işlem tipi filtresi
            data_type: Alınacak veri tipi ("response" veya "request")

        Returns:
            dict: Arama sonucunda dönen response/request verisi
        """
        return self.search_transaction(pnr_number, "pnr", transaction_type, data_type)

    def process_all_orders(self, transaction_type=None, data_type="response"):
        """
        Tüm sipariş numaralarını işler ve sonuçları DataFrame'e ekler

        Args:
            transaction_type: Tüm aramalar için geçerli olacak işlem tipi filtresi
            data_type: Alınacak veri tipi ("response", "request", "both")
        """
        if self.df is None:
            print(
                "Veri dosyası yüklenmemiş. Önce load_data() çağırın veya data_file belirtin."
            )
            return

        # Login kontrolü
        self.ensure_logged_in()

        # Filtreleme parametresi belirtilmiş ise, sınıf değişkenini güncelle
        if transaction_type is not None:
            self.transaction_type = transaction_type

        self.results = []  # Sonuçları temizle

        for index, row in self.df.iterrows():
            order_number = str(row["Order"])
            print(f"İşleniyor: {index+1}/{len(self.df)} - Order: {order_number}")

            # Arama yap
            result = self.search_transaction(
                order_number, "order", transaction_type, data_type
            )
            self.results.append(result)

            # Arama formunun temizlenmesi için sayfayı yenile
            self.driver.refresh()
            time.sleep(1)

        # Sonuçları DataFrame'e ekle
        if data_type.lower() == "both":
            self.df["Request"] = [r.get("request", None) for r in self.results]
            self.df["Response"] = [r.get("response", None) for r in self.results]
        else:
            self.df[data_type.capitalize()] = self.results

        # Bazı önemli alanları ayrı sütunlara çıkar (sadece response için)
        if data_type.lower() in ["response", "both"]:
            response_data = (
                self.df["Response"]
                if data_type.lower() == "both"
                else self.df[data_type.capitalize()]
            )
            self.df["Status"] = response_data.apply(
                lambda x: x.get("status", None) if isinstance(x, dict) else None
            )
            self.df["Description"] = response_data.apply(
                lambda x: x.get("description", None) if isinstance(x, dict) else None
            )

        print("İşlem tamamlandı.")

    def save_results(self, output_file="search_results.xlsx"):
        """Sonuçları CSV dosyasına kaydeder"""
        self.df.to_excel(output_file, index=False)
        print(f"Sonuçlar kaydedildi: {output_file}")

    def close(self):
        """Web sürücüsünü kapatır"""
        if hasattr(self, "driver"):
            self.driver.quit()
            print("Web tarayıcı kapatıldı.")
