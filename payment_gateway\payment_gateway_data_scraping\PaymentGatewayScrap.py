import pandas as pd
import time
import json
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class PaymentGatewayScrap:
    def __init__(
        self,
        url="https://www2.thy.com/pgadmin/admin/SearchTransaction.jsf#",
        data_file="order.xlsx",
        transaction_type=None,
    ):
        """
        THY PGW işlem sorgulama otomasyonu için gerekli kurulumları yapar.

        Args:
            url: THY admin sayfası URL'i
            data_file: CSV dosyası
            transaction_type: İşlem tipi filtresi (örn: "HH" Provizyon(Başarılı) için)
        """

        self.url = url
        self.data_file = data_file
        self.transaction_type = transaction_type
        self.setup_driver()
        self.load_data()
        self.results = []

    def setup_driver(self):
        """Selenium için Chrome web sürücüsünü başlatır ve yapılandırır"""
        chrome_options = Options()
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--no-sandbox")

        service = Service()
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, 10)

    def load_data(self):
        """CSV dosyasını pandas DataFrame'e yükler"""
        if os.path.exists(self.data_file):
            self.df = pd.read_excel(self.data_file)
            print(f"Veri yüklendi: {len(self.df)} kayıt.")
        else:
            print("CSV dosyası bulunamadı, dosya yolu hatalı !.")

    def login(self, username="", password="", max_wait_time=60):
        """
        Siteye giriş yapar ve kimlik doğrulama sayfası kontrolü yapar

        Args:
            username: Giriş kullanıcı adı
            password: Giriş şifresi
            max_wait_time: Kimlik doğrulama sayfasından çıkış için maksimum bekleme süresi (saniye)
        """
        try:
            self.driver.get(self.url)
            print("Sayfaya giriş yapılıyor...")

            # Sayfanın yüklenmesini bekle
            time.sleep(2)

            # Kimlik doğrulama sayfasına yönlendirme kontrolü
            auth_url = "https://auth.thy.com/auth-web/login.jsp"
            current_url = self.driver.current_url

            if auth_url in current_url:
                print(f"Kimlik doğrulama sayfasına yönlendirildi: {current_url}")

                # Login bilgileri verildiyse giriş yap
                if username and password:
                    # Kullanıcı adı ve şifre alanlarını bul
                    try:
                        username_field = self.wait.until(
                            EC.presence_of_element_located((By.ID, "username"))
                        )
                        password_field = self.wait.until(
                            EC.presence_of_element_located((By.ID, "password"))
                        )
                        login_button = self.wait.until(
                            EC.element_to_be_clickable(
                                (By.XPATH, "//*[@id='btn_login']")
                            )
                        )

                        username_field.send_keys(username)
                        password_field.send_keys(password)
                        login_button.click()
                    except Exception as e:
                        print(f"Giriş alanları bulunamadı veya giriş yapılamadı: {e}")

                # Kimlik doğrulama sayfasından çıkış yapılana kadar bekle
                print(
                    f"Kimlik doğrulama işlemi bekleniyor (maksimum {max_wait_time} saniye içinde işlem yapılması gerekmektedir)..."
                )

                start_time = time.time()
                while auth_url in self.driver.current_url:
                    time.sleep(2)
                    if time.time() - start_time > max_wait_time:
                        print(
                            f"Kimlik doğrulama zaman aşımına uğradı ({max_wait_time} saniye)."
                        )
                        break

                print(f"Mevcut URL: {self.driver.current_url}")

            # Ana sayfa yüklendikten sonra ek bekleme
            time.sleep(3)

        except Exception as e:
            print(f"Giriş yaparken hata oluştu: {e}")

    def search_transaction(
        self,
        search_value,
        search_type="order",
        transaction_type=None,
        data_type="response",
    ):
        """
        Belirtilen sipariş numarası veya PNR ile arama yapar ve sonuçları getirir

        Args:
            search_value: Aranacak sipariş numarası veya PNR
            search_type: Arama tipi ("order" veya "pnr")
            transaction_type: Bu arama için özel işlem tipi filtresi
            data_type: Alınacak veri tipi ("response" veya "request")

        Returns:
            dict: Arama sonucunda dönen response/request verisi
        """
        try:
            if search_type == "order":
                print(f"ORDER ID numarası aranıyor: {search_value}")
                # Order input alanını bul ve doldur
                input_element = self.wait.until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//*[@id='j_idt23']/table[1]/tbody/tr[1]/td[2]/input",
                        )
                    )
                )
            elif search_type == "pnr":
                print(f"PNR aranıyor: {search_value}")
                # PNR input alanını bul ve doldur
                input_element = self.wait.until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//*[@id='j_idt23']/table[1]/tbody/tr[1]/td[4]/input",
                        )
                    )
                )
            else:
                raise ValueError(
                    f"Geçersiz arama tipi: {search_type}. 'order' veya 'pnr' olmalı."
                )

            input_element.clear()
            input_element.send_keys(search_value)

            # Date alanını kontrol et ve temizle
            try:
                date_input = self.driver.find_element(By.ID, "j_idt30InputDate")
                if date_input.get_attribute("value"):
                    print("Date alanı değeri bulundu, temizleniyor...")

                    # Date input alanına tıkla (takvimi açmak için)
                    date_input.click()

                    # "Clean" butonu görününceye kadar bekle ve tıkla
                    clean_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, "//div[text()='Clean']"))
                    )
                    clean_button.click()

                    print("Tarih alanı temizlendi.")

                    # Takvim panelinin kapanmasını bekle
                    time.sleep(1)

            except NoSuchElementException:
                print("Date alanı bulunamadı, devam ediliyor.")
            except Exception as e:
                print(f"Tarih temizlenirken hata oluştu: {e}, devam ediliyor.")

            # İşlem tipi filtresini uygula (eğer belirtilmişse)
            # Önce metoda özel parametre, yoksa sınıf değişkenini kontrol et
            filter_value = (
                transaction_type
                if transaction_type is not None
                else self.transaction_type
            )

            if filter_value:
                try:
                    # İşlem tipi dropdown'ını bul
                    transaction_type_select = self.driver.find_element(
                        By.XPATH, "//*[@id='j_idt23']/table[1]/tbody/tr[3]/td[2]/select"
                    )

                    # Select sınıfı ile dropdown'ı işle
                    from selenium.webdriver.support.ui import Select

                    select = Select(transaction_type_select)

                    # Belirtilen değeri seç
                    select.select_by_value(filter_value)
                    print(f"İşlem tipi filtresi uygulandı: {filter_value}")

                except Exception as e:
                    print(
                        f"İşlem tipi filtresi uygulanırken hata oluştu: {e}, filtresiz devam ediliyor."
                    )

            # Arama butonunu bul ve tıkla
            search_button = self.wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//input[@type='submit' and @value='Search']")
                )
            )
            search_button.click()

            # Sonuçların yüklenmesini bekle
            time.sleep(2)

            # Response veya Request linkine tıkla ve popup verisini al
            try:
                # Data type'a göre uygun linki bul
                if data_type.lower() == "response":
                    link_text = "Response"
                elif data_type.lower() == "request":
                    link_text = "Request"
                else:
                    raise ValueError(
                        f"Geçersiz data tipi: {data_type}. 'response' veya 'request' olmalı."
                    )

                data_link = self.wait.until(
                    EC.element_to_be_clickable(
                        (
                            By.XPATH,
                            f"//tr[starts-with(@id, 'modelList:0')]//a[text()='{link_text}']",
                        )
                    )
                )
                data_link.click()

                # Popup'ın açılmasını bekle
                self.wait.until(
                    EC.presence_of_element_located((By.ID, "popup_container"))
                )

                # Popup içindeki textarea'dan veriyi al
                popup_data = self.wait.until(
                    EC.presence_of_element_located((By.ID, "popupData"))
                )
                data_text = popup_data.get_attribute(
                    "value"
                )  # textarea içeriğini almak için value kullan
                print(f"{link_text} alındı.")

                # Popup'ı kapat
                try:
                    close_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, "//p/a[text()='KAPAT']"))
                    )
                    close_button.click()
                    print("Popup kapatıldı.")
                    time.sleep(1)  # Popup'ın kapanması için kısa bir bekleme
                except (NoSuchElementException, TimeoutException):
                    print(
                        "Popup kapatma butonu bulunamadı, alternatif yöntem deneniyor..."
                    )
                    try:
                        # Alternatif kapatma metodu
                        close_button = self.driver.find_element(
                            By.XPATH, "//div[@id='popup_header_controls']/a"
                        )
                        close_button.click()
                        print("Popup alternatif yöntemle kapatıldı.")
                        time.sleep(1)
                    except:
                        print("Popup kapatılamadı, devam ediliyor.")

                # JSON formatına çevir
                try:
                    data_json = json.loads(data_text)
                    return data_json
                except json.JSONDecodeError:
                    print("JSON çözümlenemedi, ham metin dönüyor.")
                    return {"raw_text": data_text}

            except TimeoutException:
                print("Sonuç bulunamadı veya popup açılmadı.")
                return {"error": "No results found"}

        except Exception as e:
            print(f"Arama sırasında hata oluştu: {e}")
            return {"error": str(e)}

    def search_transaction_both(
        self, search_value, search_type="order", transaction_type=None
    ):
        """
        Hem request hem response verilerini alır

        Args:
            search_value: Aranacak sipariş numarası veya PNR
            search_type: Arama tipi ("order" veya "pnr")
            transaction_type: Bu arama için özel işlem tipi filtresi

        Returns:
            dict: Hem request hem response verilerini içeren dict
        """
        try:
            print(f"Hem request hem response alınıyor - {search_type}: {search_value}")

            # İlk olarak request verisini al
            request_data = self.search_transaction(
                search_value, search_type, transaction_type, "request"
            )

            # Sayfayı yenile ve tekrar arama yap
            self.driver.refresh()
            time.sleep(2)

            # Response verisini al
            response_data = self.search_transaction(
                search_value, search_type, transaction_type, "response"
            )

            return {"request": request_data, "response": response_data}

        except Exception as e:
            print(f"Hem request hem response alırken hata oluştu: {e}")
            return {"error": str(e)}

    def search_order(self, order_number, transaction_type=None):
        """
        Geriye uyumluluk için eski metod. search_transaction metodunu çağırır.

        Args:
            order_number: Aranacak sipariş numarası
            transaction_type: Bu arama için özel işlem tipi filtresi

        Returns:
            dict: Arama sonucunda dönen response verisi
        """
        return self.search_transaction(
            order_number, "order", transaction_type, "response"
        )

    def search_pnr(self, pnr_number, transaction_type=None, data_type="response"):
        """
        PNR ile arama yapmak için kolaylık metodu.

        Args:
            pnr_number: Aranacak PNR numarası
            transaction_type: Bu arama için özel işlem tipi filtresi
            data_type: Alınacak veri tipi ("response" veya "request")

        Returns:
            dict: Arama sonucunda dönen response/request verisi
        """
        return self.search_transaction(pnr_number, "pnr", transaction_type, data_type)

    def search_transaction_both_efficient(
        self, search_value, search_type="order", transaction_type=None
    ):
        """
        Tek arama ile hem request hem response verilerini alır (daha verimli)

        Args:
            search_value: Aranacak sipariş numarası veya PNR
            search_type: Arama tipi ("order" veya "pnr")
            transaction_type: Bu arama için özel işlem tipi filtresi

        Returns:
            dict: Hem request hem response verilerini içeren dict
        """
        try:
            if search_type == "order":
                print(
                    f"Sipariş numarası aranıyor (hem request hem response): {search_value}"
                )
                # Order input alanını bul ve doldur
                input_element = self.wait.until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//*[@id='j_idt23']/table[1]/tbody/tr[1]/td[2]/input",
                        )
                    )
                )
            elif search_type == "pnr":
                print(f"PNR aranıyor (hem request hem response): {search_value}")
                # PNR input alanını bul ve doldur
                input_element = self.wait.until(
                    EC.presence_of_element_located(
                        (
                            By.XPATH,
                            "//*[@id='j_idt23']/table[1]/tbody/tr[1]/td[4]/input",
                        )
                    )
                )
            else:
                raise ValueError(
                    f"Geçersiz arama tipi: {search_type}. 'order' veya 'pnr' olmalı."
                )

            input_element.clear()
            input_element.send_keys(search_value)

            # Date alanını kontrol et ve temizle
            try:
                date_input = self.driver.find_element(By.ID, "j_idt30InputDate")
                if date_input.get_attribute("value"):
                    date_input.clear()
                    print("Date alanı temizlendi.")
            except NoSuchElementException:
                print("Date alanı bulunamadı, devam ediliyor.")

            # Transaction type filtresi varsa uygula
            if transaction_type or self.transaction_type:
                filter_value = transaction_type or self.transaction_type
                try:
                    transaction_select = self.wait.until(
                        EC.presence_of_element_located(
                            (
                                By.XPATH,
                                "//*[@id='j_idt23']/table[1]/tbody/tr[2]/td[2]/select",
                            )
                        )
                    )

                    # Select elementini bul ve seçeneği seç
                    from selenium.webdriver.support.ui import Select

                    select = Select(transaction_select)
                    select.select_by_value(filter_value)
                    print(f"Transaction type filtresi uygulandı: {filter_value}")

                except (NoSuchElementException, TimeoutException):
                    print("Transaction type filtresi uygulanamadı.")

            # Arama butonunu bul ve tıkla
            search_button = self.wait.until(
                EC.element_to_be_clickable(
                    (By.XPATH, "//input[@type='submit' and @value='Search']")
                )
            )
            search_button.click()

            # Sonuçların yüklenmesini bekle
            time.sleep(2)

            result = {"request": None, "response": None}

            # Önce hangi linklerin mevcut olduğunu kontrol et
            available_links = []
            try:
                # Request linkini kontrol et
                request_links = self.driver.find_elements(
                    By.XPATH,
                    "//tr[starts-with(@id, 'modelList:0')]//a[text()='Request']",
                )
                if request_links:
                    available_links.append("Request")

                # Response linkini kontrol et
                response_links = self.driver.find_elements(
                    By.XPATH,
                    "//tr[starts-with(@id, 'modelList:0')]//a[text()='Response']",
                )
                if response_links:
                    available_links.append("Response")

                print(
                    f"Mevcut linkler: {', '.join(available_links) if available_links else 'Hiçbiri'}"
                )

            except Exception as e:
                print(f"Link kontrolü sırasında hata: {e}")

            # Request linkini bul ve tıkla (eğer mevcutsa)
            if "Request" in available_links:
                try:
                    request_link = self.wait.until(
                        EC.element_to_be_clickable(
                            (
                                By.XPATH,
                                "//tr[starts-with(@id, 'modelList:0')]//a[text()='Request']",
                            )
                        )
                    )
                    request_link.click()

                    # Popup'ın açılmasını bekle
                    self.wait.until(
                        EC.presence_of_element_located((By.ID, "popup_container"))
                    )

                    # Popup içindeki textarea'dan veriyi al
                    popup_data = self.wait.until(
                        EC.presence_of_element_located((By.ID, "popupData"))
                    )
                    request_text = popup_data.get_attribute("value")
                    print("Request alındı.")

                    # Popup'ı kapat
                    try:
                        close_button = self.wait.until(
                            EC.element_to_be_clickable(
                                (By.XPATH, "//p/a[text()='KAPAT']")
                            )
                        )
                        close_button.click()
                        time.sleep(1)
                    except (NoSuchElementException, TimeoutException):
                        try:
                            close_button = self.driver.find_element(
                                By.XPATH, "//div[@id='popup_header_controls']/a"
                            )
                            close_button.click()
                            time.sleep(1)
                        except:
                            print("Request popup kapatılamadı.")

                    # JSON formatına çevir
                    try:
                        result["request"] = json.loads(request_text)
                    except json.JSONDecodeError:
                        result["request"] = {"raw_text": request_text}

                except TimeoutException:
                    print("Request linki tıklanamadı.")
                    result["request"] = {"error": "Request link click failed"}
            else:
                print("Request linki mevcut değil.")
                result["request"] = {"error": "Request link not available"}

            # Response linkini bul ve tıkla (eğer mevcutsa)
            if "Response" in available_links:
                try:
                    response_link = self.wait.until(
                        EC.element_to_be_clickable(
                            (
                                By.XPATH,
                                "//tr[starts-with(@id, 'modelList:0')]//a[text()='Response']",
                            )
                        )
                    )
                    response_link.click()

                    # Popup'ın açılmasını bekle
                    self.wait.until(
                        EC.presence_of_element_located((By.ID, "popup_container"))
                    )

                    # Popup içindeki textarea'dan veriyi al
                    popup_data = self.wait.until(
                        EC.presence_of_element_located((By.ID, "popupData"))
                    )
                    response_text = popup_data.get_attribute("value")
                    print("Response alındı.")

                    # Popup'ı kapat
                    try:
                        close_button = self.wait.until(
                            EC.element_to_be_clickable(
                                (By.XPATH, "//p/a[text()='KAPAT']")
                            )
                        )
                        close_button.click()
                        time.sleep(1)
                    except (NoSuchElementException, TimeoutException):
                        try:
                            close_button = self.driver.find_element(
                                By.XPATH, "//div[@id='popup_header_controls']/a"
                            )
                            close_button.click()
                            time.sleep(1)
                        except:
                            print("Response popup kapatılamadı.")

                    # JSON formatına çevir
                    try:
                        result["response"] = json.loads(response_text)
                    except json.JSONDecodeError:
                        result["response"] = {"raw_text": response_text}

                except TimeoutException:
                    print("Response linki tıklanamadı.")
                    result["response"] = {"error": "Response link click failed"}
            else:
                print("Response linki mevcut değil.")
                result["response"] = {"error": "Response link not available"}

            return result

        except Exception as e:
            print(f"Arama sırasında hata oluştu: {e}")
            return {"error": str(e)}

    def process_all_orders(self, transaction_type=None):
        """
        Tüm sipariş numaralarını işler ve sonuçları DataFrame'e ekler

        Args:
            transaction_type: Tüm aramalar için geçerli olacak işlem tipi filtresi
        """
        self.login()

        # Filtreleme parametresi belirtilmiş ise, sınıf değişkenini güncelle
        if transaction_type is not None:
            self.transaction_type = transaction_type

        for index, row in self.df.iterrows():
            order_number = str(row["Order"])
            print(f"İşleniyor: {index+1}/{len(self.df)} - Order: {order_number}")

            # Filtre değerini kullanarak arama yap
            response = self.search_order(order_number)
            self.results.append(response)

            # Arama formunun temizlenmesi için sayfayı yenile
            self.driver.refresh()
            time.sleep(1)

        # Sonuçları DataFrame'e ekle
        self.df["Response"] = self.results

        # Bazı önemli alanları ayrı sütunlara çıkar
        self.df["Status"] = self.df["Response"].apply(
            lambda x: x.get("status", None) if isinstance(x, dict) else None
        )
        self.df["Description"] = self.df["Response"].apply(
            lambda x: x.get("description", None) if isinstance(x, dict) else None
        )

        print("İşlem tamamlandı.")

    def save_results(self, output_file="search_results.xlsx"):
        """Sonuçları CSV dosyasına kaydeder"""
        self.df.to_excel(output_file, index=False)
        print(f"Sonuçlar kaydedildi: {output_file}")

    def close(self):
        """Web sürücüsünü kapatır"""
        if hasattr(self, "driver"):
            self.driver.quit()
            print("Web tarayıcı kapatıldı.")
