{"cells": [{"cell_type": "code", "execution_count": null, "id": "4915df63", "metadata": {}, "outputs": [], "source": ["import os\n", "import zipfile\n", "import pandas as pd\n", "import shutil\n", "from datetime import datetime\n", "\n", "# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> giriş alın\n", "network_folder = input(\"<PERSON>ğ klasörü yolunu girin: \").strip()\n", "local_temp_folder = input(\"Geçici kopya klasörü<PERSON><PERSON> girin (örn: ./temp): \").strip()\n", "tarih_baslangic = input(\"<PERSON><PERSON><PERSON><PERSON><PERSON> tarihi (YYYYMMDD): \").strip()\n", "tarih_bitis = input(\"Bitiş tarihi (YYYYMMDD): \").strip()\n", "\n", "os.makedirs(local_temp_folder, exist_ok=True)\n", "rows = []"]}, {"cell_type": "code", "execution_count": null, "id": "cad7e0dd", "metadata": {}, "outputs": [], "source": ["for fname in os.listdir(network_folder):\n", "    if fname.startswith(\"DCSHISTORY-GUNLUK-\") and fname.endswith(\".zip\"):\n", "        tarih = fname.split(\"-\")[2].split(\".\")[0]\n", "        if tarih_baslangic <= tarih <= tarih_bitis:\n", "            src_path = os.path.join(network_folder, fname)\n", "            dst_path = os.path.join(local_temp_folder, fname)\n", "            print(fname)"]}, {"cell_type": "code", "execution_count": null, "id": "9bd6b67b", "metadata": {}, "outputs": [], "source": ["\n", "for fname in os.listdir(network_folder):\n", "    if fname.startswith(\"DCSHISTORY-GUNLUK-\") and fname.endswith(\".zip\"):\n", "        tarih = fname.split(\"-\")[2].split(\".\")[0]\n", "        if tarih_baslangic <= tarih <= tarih_bitis:\n", "            src_path = os.path.join(network_folder, fname)\n", "            dst_path = os.path.join(local_temp_folder, fname)\n", "            print(f\"Kopyalanıyor: {fname}\")\n", "            shutil.copy2(src_path, dst_path)\n", "            with zipfile.ZipFile(dst_path, \"r\") as z:\n", "                for txtname in z.namelist():\n", "                    if txtname.endswith(\".TXT\"):\n", "                        with z.open(txtname) as f:\n", "                            for line in f:\n", "                                line = line.decode(\"utf-8\").strip()\n", "                                parts = line.split(\"|\")\n", "                                if len(parts) > 4:\n", "                                    rows.append({\n", "                                        \"ucus_no\": parts[0],\n", "                                        \"tarih\": parts[1],\n", "                                        \"kalkis\": parts[2],\n", "                                        \"varis\": parts[3],\n", "                                        \"yil\": parts[4],\n", "                                        \"isim\": parts[6].strip(),\n", "                                        \"soyisim\": parts[7].strip(),\n", "                                        \"pnr\": parts[13],\n", "                                        \"bilet_no\": parts[14],\n", "                                        \"dogum_tarihi\": parts[39] if len(parts) > 39 else \"\",\n", "                                    })\n", "            os.remove(dst_path)\n", "            print(\"Kopyalama tamamlandı.Dosyalar Expire edildi\")"]}, {"cell_type": "code", "execution_count": null, "id": "d14132f2", "metadata": {}, "outputs": [], "source": ["\n", "# Sonuçları kaydet\n", "df = pd.DataFrame(rows)\n", "rapor_adi = f\"dcs_rapor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx\"\n", "df.to_excel(rapor_adi, index=False)\n", "print(f\"<PERSON><PERSON> o<PERSON>: {rapor_adi}\")\n", "\n", "# commit: DCS zipleri kopyala ve raporla"]}, {"cell_type": "code", "execution_count": null, "id": "a3ea6d5f", "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "ce614fd1", "metadata": {}, "outputs": [], "source": ["\n", "if df.empty:\n", "    print(\"<PERSON>ç yolcu veya uçuş bulunamadı.\")\n", "else:\n", "    ucuslar = df.groupby(['ucus_no', 'tarih']).size().reset_index(name='yolcu_sayisi')\n", "    print(f\"Toplam {ucuslar.shape[0]} farklı uçuş (tarih) bulundu.\")\n", "    for _, row in ucuslar.iterrows():\n", "        print(f\"Uçuş: {row['ucus_no']} Saat: {row['tarih']} - <PERSON><PERSON><PERSON> sayıs<PERSON>: {row['yolcu_sayisi']}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}