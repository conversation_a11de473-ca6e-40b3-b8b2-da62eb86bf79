import logging
import os
from datetime import datetime


def get_logger(name="TroyaConnection", log_level=logging.INFO):
    """
    Troya bağlantısı için harici logger oluşturur

    Args:
        name: Logger adı
        log_level: Log seviyesi (logging.DEBUG, logging.INFO, vb.)

    Returns:
        <PERSON><PERSON><PERSON>landırılmış logger nesnesi
    """
    logger = logging.getLogger(name)

    # Log seviyesini ayarla
    logger.setLevel(log_level)

    # Eğer henüz handler yoksa ekle
    if not logger.handlers:
        # Log dosya adı - günlük olarak
        log_filename = f"troya_{datetime.now().strftime('%Y%m%d')}.log"
        log_dir = "logs"

        # Log klasörü yoksa oluştur
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        log_path = os.path.join(log_dir, log_filename)

        # Dosya handler'ı
        file_handler = logging.FileHandler(log_path, encoding="utf-8")
        file_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
        logger.addHandler(file_handler)

        # Konsol handler'ı
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter("%(levelname)s: %(message)s"))
        logger.addHandler(console_handler)

    return logger
