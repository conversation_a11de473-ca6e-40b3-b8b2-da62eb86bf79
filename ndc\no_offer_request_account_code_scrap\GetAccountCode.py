import time
import pandas as pd
import json
import requests
from requests.auth import HTTPBasicAuth
import xml.etree.ElementTree as ET

# Basic authentication credentials
username = input("Kullanıcı Adınızı girin: ")
password = input("Şifrenizi girin: ")


# JSON dosyasını okuyun
with open('Error Data.json', 'r', encoding='utf-8') as file:
    data = json.load(file)

# 'hits' içindeki veriyi çıkarın
hits_data = data['hits']['hits']

# 'hits' içindeki veriyi Pandas DataFrame'e dönüştürün
df = pd.json_normalize(hits_data)

###################################################### GRAYLOG Search ######################################################

# Graylog URL
url = "https://graylogflorya.thy.com/api/views/search"


trx_id = ""
# İstek parametreleri
payload = {
    "queries": [
        {
            "query": {
                "type": "elasticsearch",
                "query_string": f'"{trx_id}" AND logType:REQUEST'
            },
            "timerange": {
                "type": "relative",
                "range": 0
            },
            "filter": {
                "type": "or",
                "filters": [
                    {
                        "type": "stream",
                        "id": "654b3a31bc996e003ad06de6"
                    }
                ]
            },
            "search_types": [
                {

                    "streams": [],
                    "limit": 150,
                    "offset": 0,
                    "sort": [
                        {
                            "field": "timestamp",
                            "order": "DESC"
                        }
                    ],
                    "fields": [],
                    "decorators": [],
                    "type": "messages",
                    "filters": []
                }
            ]
        }
    ]
}

jsondata = {"parameter_bindings": {}}


# Header bilgileri
headers = {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "X-Requested-By": "Graylog API"
}

namespace = {'ns2': 'http://www.iata.org/IATA/2015/EASD/00/IATA_OffersAndOrdersCommonTypes'}

# DataFrame'e yeni bir sütun ekleyin
df['accountID'] = None

# Tüm trxId değerlerini gezmek için döngü
for index, row in df.iterrows():
    trx_id = row['_source.trxId']
    
    # İstek parametreleri
    payload['queries'][0]['query']['query_string'] = f'"{trx_id}" AND logType:REQUEST'
    
    # İstek gönder
    response = requests.post(url, json=payload, headers=headers, auth=(username, password))
    
    if response.status_code == 201:
        response_data = response.json()
        search_id = response_data['id']
        
        execute_url = f"{url}/{search_id}/execute"
        execute_response = requests.post(execute_url, json=jsondata, headers=headers, auth=(username, password))
        
        if execute_response.status_code == 201:
            execute_data = execute_response.json()
            result_id = list(execute_data['results'].keys())[0]
            search_types = execute_data['results'][result_id]['search_types']
            random_id = list(search_types.keys())[0]
            messages = search_types[random_id]['messages']
            if messages:
                message_value = messages[0]['message']
                message_xml = message_value['message']
                soap_index = message_xml.find('<soap:')
                if soap_index != -1:
                    message_xml = message_xml[soap_index:]
                root = ET.fromstring(message_xml)
                account_element = root.find('.//ns2:ProgramAccount/ns2:AccountID', namespace)
                if account_element is not None:
                    code = account_element.text
                    df.at[index, 'accountID'] = code
                    print(f"{index} satırdaki trxId: {trx_id} için accountID: {code}")
                else:
                    df.at[index, 'accountID'] = None
            else:
                df.at[index, 'accountID'] = None
        else:
            df.at[index, 'accountID'] = None
    else:
        df.at[index, 'accountID'] = None

max_retries = 6
retry_delay = 10  # seconds

# Mevcut tarih ve saati alın
current_time = time.strftime("%d-%m-%Y")

for attempt in range(max_retries):
    try:
        df.to_excel(f'./Data/AccountCode_{current_time}.xlsx', index=False)
        print("Dosya başarıyla kaydedildi.")
        break
    except Exception as e:
        print(f"Hata oluştu: {e}")
        if attempt < max_retries - 1:
            print(f"{retry_delay} saniye bekleniyor ve tekrar deneniyor...")
            time.sleep(retry_delay)
        else:
            print("Maksimum deneme sayısına ulaşıldı. Dosya kaydedilemedi.")


###################################################### List Match ######################################################

print("Excel dosyaları karşılaştırılıyor...")

# Excel dosyalarını yükleyin
error_df = df
authorized_df = pd.read_excel('CorprateCode List.xlsx')

# Eşleşen verileri kontrol edin ve yeni bir sütun ekleyin
error_df['Match'] = error_df.apply(lambda row: any(
    (row['accountID'] == auth_row['CORPORATE_ID'] and row['_source.sellerAgencyPseudoCityCode'] == auth_row['PCC_CODE'])
    for _, auth_row in authorized_df.iterrows()
), axis=1)

# Eşleşen verileri True olarak işaretleyin
error_df['Match'] = error_df['Match'].apply(lambda x: 'True' if x else 'False')

error_df.to_excel(f'AccountCode_Corprate_Matches{current_time}.xlsx', index=False)