# !pip install openpyxl

from PaymentGatewayScrap import PaymentGatewayScrap

# Toplu işlem için
search_bot = PaymentGatewayScrap(data_file="order.xlsx", auto_login=True)
try:
    search_bot.process_all_orders(transaction_type="AA", data_type="response")
    search_bot.save_results(output_file="output.xlsx")
finally:
    search_bot.close()

# Tek arama için
scraper = PaymentGatewayScrap(auto_login=False)
try:
    # Manuel login yap
    scraper.login()
    
    # Login kontrolü
    if not scraper.is_logged_in:
        input("Login tamamlandıktan sonra Enter'a basın: ")
        scraper.is_logged_in = True
    
    # PNR ile arama
    result = scraper.search_transaction(
        search_value="UGYGLE",
        search_type="pnr",
        transaction_type="AA",
        data_type="both",
        get_all_results=True
    )
    
    print("Sonuç:", result)
    
finally:
    scraper.close()

result