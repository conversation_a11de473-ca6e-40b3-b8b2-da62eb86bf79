# !pip install openpyxl

# <PERSON><PERSON><PERSON><PERSON><PERSON>
from PaymentGatewayScrap import PaymentGatewayScrap

# Toplu işlem için - Basit (Tek sonuç)
search_bot = PaymentGatewayScrap(data_file="order.xlsx", auto_login=True)
try:
    search_bot.process_all_orders(
        transaction_type="AA", 
        data_type="response",
        get_all_results=False  # Sadece ilk sonuç
    )
    search_bot.save_results(output_file="output_simple.xlsx")
finally:
    search_bot.close()

# Tek arama için
scraper = PaymentGatewayScrap(auto_login=False)
try:
    # Manuel login yap
    scraper.login()
    
    # Login kontrolü
    if not scraper.is_logged_in:
        input("Login tamamlandıktan sonra Enter'a basın: ")
        scraper.is_logged_in = True
    
    # PNR ile arama
    result = scraper.search_transaction(
        search_value="UGYGLE",
        search_type="pnr",
        transaction_type="AA",
        data_type="both"
    )
    
    print("Sonuç:", result)
    
finally:
    scraper.close()

# Toplu işlem için - Çoklu Sonuç (Her order için tüm sonuçları al)
search_bot = PaymentGatewayScrap(data_file="order.xlsx", auto_login=True)
try:
    search_bot.process_all_orders(
        transaction_type="AA", 
        data_type="both",  # Hem request hem response
        get_all_results=True  # Tüm sonuçları al
    )
    search_bot.save_results(output_file="output_multiple.xlsx")
    
    # Özet bilgi
    print(f"Toplam satır sayısı: {len(search_bot.df)}")
    print("İlk 5 satır:")
    print(search_bot.df[['Order', 'Result_Index', 'Total_Results', 'Status']].head())
    
finally:
    search_bot.close()

# PNR Bulk İşlemi (pnr.xlsx dosyası gerekli)
# Dosya formatı: PNR sütunu ile UGYGLE, ABCDEF gibi değerler
search_bot = PaymentGatewayScrap(data_file="pnr.xlsx", auto_login=True)
try:
    search_bot.process_all_orders(
        transaction_type="AA", 
        data_type="both",
        get_all_results=True,
        search_type="pnr"  # PNR ile arama
    )
    search_bot.save_results(output_file="pnr_results.xlsx")
    
    print(f"PNR işlemi tamamlandı: {len(search_bot.df)} satır")
    
finally:
    search_bot.close()