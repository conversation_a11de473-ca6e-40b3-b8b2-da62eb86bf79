# !pip install openpyxl

# <PERSON><PERSON><PERSON><PERSON><PERSON> yeniden yükle (gerekirse)
import importlib
import sys
import os

# <PERSON><PERSON><PERSON> modül daha önce yüklenmişse yeniden yükle
if 'PaymentGatewayScrap' in sys.modules:
    import PaymentGatewayScrap
    importlib.reload(PaymentGatewayScrap)

from PaymentGatewayScrap import PaymentGatewayScrap
import pandas as pd

print("✅ Modüller yüklendi")

# Örnek Excel dosyaları oluştur
import pandas as pd
import os

# Order dosyası oluştur
if not os.path.exists('order.xlsx'):
    order_data = {
        'Order': ['12345', '67890', 'ABC123', 'DEF456', 'GHI789']
    }
    df_order = pd.DataFrame(order_data)
    df_order.to_excel('order.xlsx', index=False)
    print("✅ order.xlsx oluşturuldu")
else:
    print("📁 order.xlsx zaten mevcut")

# PNR dosyası oluştur
if not os.path.exists('pnr.xlsx'):
    pnr_data = {
        'PNR': ['UGYGLE', 'ABCDEF', 'XYZPQR', 'MNOPQR', 'STUVWX']
    }
    df_pnr = pd.DataFrame(pnr_data)
    df_pnr.to_excel('pnr.xlsx', index=False)
    print("✅ pnr.xlsx oluşturuldu")
else:
    print("📁 pnr.xlsx zaten mevcut")

print("\n📋 Dosya içerikleri:")
if os.path.exists('order.xlsx'):
    print("\norder.xlsx:")
    print(pd.read_excel('order.xlsx'))

if os.path.exists('pnr.xlsx'):
    print("\npnr.xlsx:")
    print(pd.read_excel('pnr.xlsx'))

# Toplu işlem için - Basit (Tek sonuç)
search_bot = PaymentGatewayScrap(data_file="order.xlsx", auto_login=True)
try:
    search_bot.process_all_orders(
        transaction_type="AA", 
        data_type="response",
        get_all_results=False  # Sadece ilk sonuç
    )
    search_bot.save_results(output_file="output_simple.xlsx")
finally:
    search_bot.close()

# Tek arama için - PNR örneği
scraper = PaymentGatewayScrap(auto_login=False)
try:
    # Manuel login yap
    scraper.login()
    
    # Login kontrolü
    if not scraper.is_logged_in:
        print("⚠️ Manuel login gerekli!")
        input("Login tamamlandıktan sonra Enter'a basın: ")
        scraper.is_logged_in = True
    
    print("🔍 PNR ile arama yapılıyor...")
    
    # PNR ile arama - Tüm sonuçları al
    result = scraper.search_transaction(
        search_value="UGYGLE",
        search_type="pnr",
        transaction_type="AA",
        data_type="both",
        get_all_results=True  # Çoklu sonuç varsa hepsini al
    )
    
    # Sonuç analizi
    if isinstance(result, dict) and "total_results" in result:
        print(f"\n📊 Toplam {result['total_results']} sonuç bulundu")
        print(f"📋 {len(result['results'])} sonuç alındı")
        
        for i, res in enumerate(result['results']):
            print(f"\n--- Sonuç {i+1} ---")
            if 'request' in res and res['request']:
                print(f"Request: {'✅' if 'error' not in res['request'] else '❌'}")
            if 'response' in res and res['response']:
                print(f"Response: {'✅' if 'error' not in res['response'] else '❌'}")
    else:
        print(f"\n📋 Tek sonuç:")
        if isinstance(result, dict):
            if 'request' in result:
                print(f"Request: {'✅' if 'error' not in result.get('request', {}) else '❌'}")
            if 'response' in result:
                print(f"Response: {'✅' if 'error' not in result.get('response', {}) else '❌'}")
    
    print(f"\n📄 Ham sonuç: {str(result)[:200]}...")
    
finally:
    scraper.close()
    print("🔒 Tarayıcı kapatıldı")

# Toplu işlem için - Çoklu Sonuç (Her order için tüm sonuçları al)
search_bot = PaymentGatewayScrap(data_file="order.xlsx", auto_login=True)
try:
    search_bot.process_all_orders(
        transaction_type="AA", 
        data_type="both",  # Hem request hem response
        get_all_results=True  # Tüm sonuçları al
    )
    search_bot.save_results(output_file="output_multiple.xlsx")
    
    # Özet bilgi
    print(f"Toplam satır sayısı: {len(search_bot.df)}")
    print("İlk 5 satır:")
    print(search_bot.df[['Order', 'Result_Index', 'Total_Results', 'Status']].head())
    
finally:
    search_bot.close()

# PNR Bulk İşlemi (pnr.xlsx dosyası gerekli)
# Dosya formatı: PNR sütunu ile UGYGLE, ABCDEF gibi değerler
search_bot = PaymentGatewayScrap(data_file="order.xlsx", auto_login=True)
try:
    search_bot.process_all_orders(
        transaction_type="AA", 
        data_type="both",
        get_all_results=True,
        search_type="pnr"  # PNR ile arama
    )
    search_bot.save_results(output_file="pnr_results.xlsx")
    
    print(f"PNR işlemi tamamlandı: {len(search_bot.df)} satır")
    
finally:
    search_bot.close()