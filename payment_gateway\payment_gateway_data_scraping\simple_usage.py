#!/usr/bin/env python3
"""
THY Payment Gateway Basit Kullanım Örneği
"""

from PaymentGatewayScrap import PaymentGatewayScrap

def main():
    """Ana fonksiyon"""
    scraper = None
    try:
        # 1. Scraper'ı başlat
        print("Chrome tarayıcısı başlatılıyor...")
        scraper = PaymentGatewayScrap(auto_login=False)
        
        # 2. Manuel login yap
        print("Login sayfasına yönlendiriliyor...")
        print("Tarayıcıda açılan sayfada login yapın...")
        scraper.login()
        
        # Login kontrolü
        if not scraper.is_logged_in:
            input("Login tamamlandıktan sonra Enter'a basın: ")
            scraper.is_logged_in = True
        
        # 3. Tek arama yap
        print("\nPNR araması yapılıyor...")
        result = scraper.search_transaction(
            search_value="UGYGLE",
            search_type="pnr", 
            transaction_type="AA",
            data_type="both"  # Hem request hem response
        )
        
        print("Sonuç:")
        print(f"Request: {'✅' if result.get('request') and 'error' not in result['request'] else '❌'}")
        print(f"Response: {'✅' if result.get('response') and 'error' not in result['response'] else '❌'}")
        
        # Detaylı sonuç
        if result.get('request'):
            print(f"\nRequest veri tipi: {type(result['request'])}")
        if result.get('response'):
            print(f"Response veri tipi: {type(result['response'])}")
            
    except Exception as e:
        print(f"Hata: {e}")
        
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    main()
