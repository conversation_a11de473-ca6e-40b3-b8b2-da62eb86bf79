{"cells": [{"cell_type": "code", "execution_count": null, "id": "ca806c95", "metadata": {}, "outputs": [], "source": ["# pip install pywin32 pyautogui"]}, {"cell_type": "code", "execution_count": null, "id": "fe9d7f0d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "import getpass\n", "from ExtraConnection import ExtraConnection,setup_logger\n", "\n", "# Lo<PERSON>'<PERSON> ayarla\n", "logger = setup_logger()\n", "logger.info(\"TROYA bağlantısı başlatılıyor...\")"]}, {"cell_type": "markdown", "id": "47f488b5", "metadata": {}, "source": ["## TROYA Terminali Bağlantısı Kurma\n", "\n", "EXTRA! uygulaması ile TROYA sistemine giriş yapma."]}, {"cell_type": "code", "execution_count": null, "id": "31c2e1e7", "metadata": {}, "outputs": [], "source": ["username=input(\"Kullanıcı Adı Girin (SON\\ dan sonrası girilmelidir!) : \")\n", "password = getpass.getpass(\"Şifre: \")"]}, {"cell_type": "code", "execution_count": null, "id": "ca3acbcd", "metadata": {}, "outputs": [], "source": ["troya = ExtraConnection()\n", "if troya.connect():\n", "    troya.login(username,password)\n", "else:\n", "    logger.error(\"Bağlantı kurulamadı! Extra Terminal in açık olduğundan emin olun.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}