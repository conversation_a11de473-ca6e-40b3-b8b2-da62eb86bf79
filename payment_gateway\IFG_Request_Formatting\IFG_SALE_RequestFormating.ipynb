JSON ="""


"""

def format_amount(amount):
    if amount:
        return "{}.{:02d}".format(int(amount / 100), int(amount % 100))
    else:
        return ''

import json
import pandas as pd

def json_to_df(JSON):
    veri = json.loads(JSON)

    data = []
    for order in veri['orders']:
        for flight_record in order['flightRecords']:
            for itinerary in flight_record['documentDetails'].get('itinerary', []):
                data.append({
                    'trackID': veri.get('trackID', ''),
                    'uniqueTransactionID': veri.get('uniqueTransactionID', ''),
                    'basicInformation.agentCode': veri['basicInformation'].get('agentCode', ''),
                    'basicInformation.channel': veri['basicInformation'].get('channel', ''),
                    'basicInformation.clientID': veri['basicInformation'].get('clientID', ''),
                    'basicInformation.countryCode': veri['basicInformation'].get('countryCode', ''),
                    'basicInformation.nonTicketInd': veri['basicInformation'].get('nonTicketInd', ''),
                    'passengerRecord.name': flight_record['passengerRecord'].get('name', ''),
                    'passengerRecord.pnr': flight_record['passengerRecord'].get('pnr', ''),
                    'documentID.ticketDocumentNumber': flight_record['documentID'].get('ticketDocumentNumber', ''),
                    'itinerary.carrier': itinerary.get('carrier', ''),
                    'itinerary.originCode': itinerary.get('originCode', ''),
                    'itinerary.destinationCode': itinerary.get('destinationCode', ''),
                    'itinerary.flightDate': itinerary.get('flightDate', ''),
                    'itinerary.flightTime': itinerary.get('flightTime', ''),
                    'itinerary.flightNumber': itinerary.get('flightNumber', ''),
                    'itinerary.reservationBooking': itinerary.get('reservationBooking', ''),
                    'itinerary.segmentIdentifier': itinerary.get('segmentIdentifier', ''),
                    'itinerary.fareBasisTicketDesignator': itinerary.get('fareBasisTicketDesignator', ''),
                    'flightRecord.tourCode': flight_record.get('tourCode', ''),
                    'additionalInformation.endoresementsRestrictions': order['additionalInformation'].get('endoresementsRestrictions', ''),
                    'additionalInformation.paymentInformationLine1': order['additionalInformation'].get('paymentInformationLine1', ''),
                    'additionalInformation.paymentInformationLine2': order['additionalInformation'].get('paymentInformationLine2', ''),
                    'Exchange': 'F',
                })

                if 'exchangeDetails' in flight_record['documentDetails']:
                    data[-1]['Exchange'] = 'T'
                    exchange_details = flight_record['documentDetails']['exchangeDetails'][0]
                    data[-1]['exchangeDetails.relatedTicketDocumentNumber'] = exchange_details.get('relatedTicketDocumentNumber', '')
                    data[-1]['exchangeDetails.relatedTicketDocumentCouponNumberIdentifier'] = exchange_details.get('relatedTicketDocumentCouponNumberIdentifier', '')
                    data[-1]['exchangeDetails.originalTrackID'] = exchange_details.get('originalTrackID', '')

                for sales_info in order['additionalSalesInformation']:
                    data[-1]['additionalSalesInformation.equivalentFarePaid'] = sales_info.get('equivalentFarePaid', '')
                    data[-1]['additionalSalesInformation.fare'] = sales_info.get('fare', '')
                    data[-1]['additionalSalesInformation.fareCalculationArea1'] = sales_info.get('fareCalculationArea1', '')
                    data[-1]['additionalSalesInformation.fareCalculationModeIndicator'] = sales_info.get('fareCalculationModeIndicator', '')
                    data[-1]['additionalSalesInformation.fareCalculationPricingIndicator'] = sales_info.get('fareCalculationPricingIndicator', '')
                    data[-1]['additionalSalesInformation.originalIssueAgentNumericCode'] = sales_info.get('originalIssueAgentNumericCode', '')
                    data[-1]['additionalSalesInformation.originalIssueDate'] = sales_info.get('originalIssueDate', '')
                    data[-1]['additionalSalesInformation.originalIssueLocationCityCode'] = sales_info.get('originalIssueLocationCityCode', '')
                    data[-1]['additionalSalesInformation.originalTicketDocumentNumber'] = sales_info.get('originalTicketDocumentNumber', '')
                    data[-1]['additionalSalesInformation.total'] = sales_info.get('total', '')

                for tax in order['monetaryAmounts']['taxFeeRecords']:
                    data[-1]['taxFeeRecords.' + tax['type']] = format_amount(tax.get('amount', ''))

                for reported_payment in order['reportedFormOfPayment']:
                    data[-1]['reportedFormOfPayment.reportedTransactionIdentifier'] = reported_payment.get('transactionIdentifier', '')
                    data[-1]['reportedFormOfPayment.reportedType'] = reported_payment.get('type', '')
                    data[-1]['reportedFormOfPayment.reportedAmount'] = format_amount(reported_payment.get('amount', ''))
                    data[-1]['reportedFormOfPayment.reportedCurrency'] = reported_payment.get('currency', '')
                    data[-1]['reportedFormOfPayment.reportedAccountNumber'] = reported_payment.get('accountNumber', '')
                    data[-1]['reportedFormOfPayment.reportedApprovalCode'] = reported_payment.get('approvalCode', '')
                    data[-1]['reportedFormOfPayment.reportedAuthorizedAmount'] = reported_payment.get('authorizedAmount', '')

                for payment in veri['formOfPayment']:
                    data[-1]['formOfPayment.accountNumber'] = payment.get('accountNumber', '')
                    data[-1]['formOfPayment.amount'] = format_amount(payment.get('amount', ''))
                    data[-1]['formOfPayment.currency'] = payment.get('currency', '')
                    data[-1]['formOfPayment.transactionIdentifier'] = payment.get('transactionIdentifier', '')
                    data[-1]['formOfPayment.type'] = payment.get('type', '')

    df = pd.DataFrame(data)

    return df

data=json_to_df(JSON)

data.to_excel("output.xlsx", index=False)  

execfile=input("Excel dosya yolunu girin (a.xlsx): ")
json_column=input("Excel JSON Stununu girin : ")
excel_data = pd.read_excel(execfile)
json_data = excel_data[json_column].tolist()

dataframes = []
for json_str in json_data:
    df = json_to_df(json_str)
    dataframes.append(df)

combined_df = pd.concat(dataframes, ignore_index=True)
combined_df.to_excel("output.xlsx", index=False)  