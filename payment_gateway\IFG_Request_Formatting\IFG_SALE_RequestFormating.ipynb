{"cells": [{"cell_type": "code", "execution_count": null, "id": "2b1a6bd8", "metadata": {}, "outputs": [], "source": ["JSON =\"\"\"\n", "\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "220f96b2", "metadata": {}, "outputs": [], "source": ["def format_amount(amount):\n", "    if amount:\n", "        return \"{}.{:02d}\".format(int(amount / 100), int(amount % 100))\n", "    else:\n", "        return ''"]}, {"cell_type": "code", "execution_count": null, "id": "f4145586", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "\n", "def json_to_df(JSON):\n", "    veri = json.loads(JSON)\n", "\n", "    data = []\n", "    for order in veri['orders']:\n", "        for flight_record in order['flightRecords']:\n", "            for itinerary in flight_record['documentDetails'].get('itinerary', []):\n", "                data.append({\n", "                    'trackID': veri.get('trackID', ''),\n", "                    'uniqueTransactionID': veri.get('uniqueTransactionID', ''),\n", "                    'basicInformation.agentCode': veri['basicInformation'].get('agentCode', ''),\n", "                    'basicInformation.channel': veri['basicInformation'].get('channel', ''),\n", "                    'basicInformation.clientID': veri['basicInformation'].get('clientID', ''),\n", "                    'basicInformation.countryCode': veri['basicInformation'].get('countryCode', ''),\n", "                    'basicInformation.nonTicketInd': veri['basicInformation'].get('nonTicketInd', ''),\n", "                    'passengerRecord.name': flight_record['passengerRecord'].get('name', ''),\n", "                    'passengerRecord.pnr': flight_record['passengerRecord'].get('pnr', ''),\n", "                    'documentID.ticketDocumentNumber': flight_record['documentID'].get('ticketDocumentNumber', ''),\n", "                    'itinerary.carrier': itinerary.get('carrier', ''),\n", "                    'itinerary.originCode': itinerary.get('originCode', ''),\n", "                    'itinerary.destinationCode': itinerary.get('destinationCode', ''),\n", "                    'itinerary.flightDate': itinerary.get('flightDate', ''),\n", "                    'itinerary.flightTime': itinerary.get('flightTime', ''),\n", "                    'itinerary.flightNumber': itinerary.get('flightNumber', ''),\n", "                    'itinerary.reservationBooking': itinerary.get('reservationBooking', ''),\n", "                    'itinerary.segmentIdentifier': itinerary.get('segmentIdentifier', ''),\n", "                    'itinerary.fareBasisTicketDesignator': itinerary.get('fareBasisTicketDesignator', ''),\n", "                    'flightRecord.tourCode': flight_record.get('tourCode', ''),\n", "                    'additionalInformation.endoresementsRestrictions': order['additionalInformation'].get('endoresementsRestrictions', ''),\n", "                    'additionalInformation.paymentInformationLine1': order['additionalInformation'].get('paymentInformationLine1', ''),\n", "                    'additionalInformation.paymentInformationLine2': order['additionalInformation'].get('paymentInformationLine2', ''),\n", "                    'Exchange': 'F',\n", "                })\n", "\n", "                if 'exchangeDetails' in flight_record['documentDetails']:\n", "                    data[-1]['Exchange'] = 'T'\n", "                    exchange_details = flight_record['documentDetails']['exchangeDetails'][0]\n", "                    data[-1]['exchangeDetails.relatedTicketDocumentNumber'] = exchange_details.get('relatedTicketDocumentNumber', '')\n", "                    data[-1]['exchangeDetails.relatedTicketDocumentCouponNumberIdentifier'] = exchange_details.get('relatedTicketDocumentCouponNumberIdentifier', '')\n", "                    data[-1]['exchangeDetails.originalTrackID'] = exchange_details.get('originalTrackID', '')\n", "\n", "                for sales_info in order['additionalSalesInformation']:\n", "                    data[-1]['additionalSalesInformation.equivalentFarePaid'] = sales_info.get('equivalentFarePaid', '')\n", "                    data[-1]['additionalSalesInformation.fare'] = sales_info.get('fare', '')\n", "                    data[-1]['additionalSalesInformation.fareCalculationArea1'] = sales_info.get('fareCalculationArea1', '')\n", "                    data[-1]['additionalSalesInformation.fareCalculationModeIndicator'] = sales_info.get('fareCalculationModeIndicator', '')\n", "                    data[-1]['additionalSalesInformation.fareCalculationPricingIndicator'] = sales_info.get('fareCalculationPricingIndicator', '')\n", "                    data[-1]['additionalSalesInformation.originalIssueAgentNumericCode'] = sales_info.get('originalIssueAgentNumericCode', '')\n", "                    data[-1]['additionalSalesInformation.originalIssueDate'] = sales_info.get('originalIssueDate', '')\n", "                    data[-1]['additionalSalesInformation.originalIssueLocationCityCode'] = sales_info.get('originalIssueLocationCityCode', '')\n", "                    data[-1]['additionalSalesInformation.originalTicketDocumentNumber'] = sales_info.get('originalTicketDocumentNumber', '')\n", "                    data[-1]['additionalSalesInformation.total'] = sales_info.get('total', '')\n", "\n", "                for tax in order['monetaryAmounts']['taxFeeRecords']:\n", "                    data[-1]['taxFeeRecords.' + tax['type']] = format_amount(tax.get('amount', ''))\n", "\n", "                for reported_payment in order['reportedFormOfPayment']:\n", "                    data[-1]['reportedFormOfPayment.reportedTransactionIdentifier'] = reported_payment.get('transactionIdentifier', '')\n", "                    data[-1]['reportedFormOfPayment.reportedType'] = reported_payment.get('type', '')\n", "                    data[-1]['reportedFormOfPayment.reportedAmount'] = format_amount(reported_payment.get('amount', ''))\n", "                    data[-1]['reportedFormOfPayment.reportedCurrency'] = reported_payment.get('currency', '')\n", "                    data[-1]['reportedFormOfPayment.reportedAccountNumber'] = reported_payment.get('accountNumber', '')\n", "                    data[-1]['reportedFormOfPayment.reportedApprovalCode'] = reported_payment.get('approvalCode', '')\n", "                    data[-1]['reportedFormOfPayment.reportedAuthorizedAmount'] = reported_payment.get('authorizedAmount', '')\n", "\n", "                for payment in veri['formOfPayment']:\n", "                    data[-1]['formOfPayment.accountNumber'] = payment.get('accountNumber', '')\n", "                    data[-1]['formOfPayment.amount'] = format_amount(payment.get('amount', ''))\n", "                    data[-1]['formOfPayment.currency'] = payment.get('currency', '')\n", "                    data[-1]['formOfPayment.transactionIdentifier'] = payment.get('transactionIdentifier', '')\n", "                    data[-1]['formOfPayment.type'] = payment.get('type', '')\n", "\n", "    df = pd.DataFrame(data)\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "d5b7f6ce", "metadata": {}, "outputs": [], "source": ["data=json_to_df(JSON)"]}, {"cell_type": "code", "execution_count": null, "id": "b509099d", "metadata": {}, "outputs": [], "source": ["data.to_excel(\"output.xlsx\", index=False)  "]}, {"cell_type": "markdown", "id": "a646b0e8", "metadata": {}, "source": ["## Excel Bulk JSON TO DF"]}, {"cell_type": "code", "execution_count": null, "id": "1acdc70e", "metadata": {}, "outputs": [], "source": ["execfile=input(\"Excel dosya yolunu girin (a.xlsx): \")\n", "json_column=input(\"Excel JSON Stununu girin : \")\n", "excel_data = pd.read_excel(execfile)\n", "json_data = excel_data[json_column].tolist()\n", "\n", "dataframes = []\n", "for json_str in json_data:\n", "    df = json_to_df(json_str)\n", "    dataframes.append(df)\n", "\n", "combined_df = pd.concat(dataframes, ignore_index=True)\n", "combined_df.to_excel(\"output.xlsx\", index=False)  "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}